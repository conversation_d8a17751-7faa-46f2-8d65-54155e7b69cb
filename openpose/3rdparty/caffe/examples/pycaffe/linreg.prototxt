name: 'LinearRegressionExample'
# define a simple network for linear regression on dummy data
# that computes the loss by a PythonLayer.
layer {
  type: 'DummyData'
  name: 'x'
  top: 'x'
  dummy_data_param {
    shape: { dim: 10 dim: 3 dim: 2 }
    data_filler: { type: 'gaussian' }
  }
}
layer {
  type: 'DummyData'
  name: 'y'
  top: 'y'
  dummy_data_param {
    shape: { dim: 10 dim: 3 dim: 2 }
    data_filler: { type: 'gaussian' }
  }
}
# include InnerProduct layers for parameters
# so the net will need backward
layer {
  type: 'InnerProduct'
  name: 'ipx'
  top: 'ipx'
  bottom: 'x'
  inner_product_param {
    num_output: 10
    weight_filler { type: 'xavier' }
  }
}
layer {
  type: 'InnerProduct'
  name: 'ipy'
  top: 'ipy'
  bottom: 'y'
  inner_product_param {
    num_output: 10
    weight_filler { type: 'xavier' }
  }
}
layer {
  type: 'Python'
  name: 'loss'
  top: 'loss'
  bottom: 'ipx'
  bottom: 'ipy'
  python_param {
    # the module name -- usually the filename -- that needs to be in $PYTHONPATH
    module: 'pyloss'
    # the layer name -- the class name in the module
    layer: 'EuclideanLossLayer'
  }
  # set loss weight so Caffe knows this is a loss layer.
  # since PythonLayer inherits directly from Layer, this isn't automatically
  # known to Caffe
  loss_weight: 1
}
