{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fine-tuning a Pretrained Network for Style Recognition\n", "\n", "In this example, we'll explore a common approach that is particularly useful in real-world applications: take a pre-trained Caffe network and fine-tune the parameters on your custom data.\n", "\n", "The advantage of this approach is that, since pre-trained networks are learned on a large set of images, the intermediate layers capture the \"semantics\" of the general visual appearance. Think of it as a very powerful generic visual feature that you can treat as a black box. On top of that, only a relatively small amount of data is needed for good performance on the target task."]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we will need to prepare the data. This involves the following parts:\n", "(1) Get the ImageNet ilsvrc pretrained model with the provided shell scripts.\n", "(2) Download a subset of the overall Flickr style dataset for this demo.\n", "(3) Compile the downloaded Flickr dataset into a database that Caffe can then consume."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["caffe_root = '../'  # this file should be run from {caffe_root}/examples (otherwise change this line)\n", "\n", "import sys\n", "sys.path.insert(0, caffe_root + 'python')\n", "import caffe\n", "\n", "caffe.set_device(0)\n", "caffe.set_mode_gpu()\n", "\n", "import numpy as np\n", "from pylab import *\n", "%matplotlib inline\n", "import tempfile\n", "\n", "# Helper function for deprocessing preprocessed images, e.g., for display.\n", "def deprocess_net_image(image):\n", "    image = image.copy()              # don't modify destructively\n", "    image = image[::-1]               # BGR -> RGB\n", "    image = image.transpose(1, 2, 0)  # CHW -> HWC\n", "    image += [123, 117, 104]          # (approximately) undo mean subtraction\n", "\n", "    # clamp values in [0, 255]\n", "    image[image < 0], image[image > 255] = 0, 255\n", "\n", "    # round and cast from float32 to uint8\n", "    image = np.round(image)\n", "    image = np.require(image, dtype=np.uint8)\n", "\n", "    return image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Setup and dataset download\n", "\n", "Download data required for this exercise.\n", "\n", "- `get_ilsvrc_aux.sh` to download the ImageNet data mean, labels, etc.\n", "- `download_model_binary.py` to download the pretrained reference model\n", "- `finetune_flickr_style/assemble_data.py` downloads the style training and testing data\n", "\n", "We'll download just a small subset of the full dataset for this exercise: just 2000 of the 80K images, from 5 of the 20 style categories.  (To download the full dataset, set `full_dataset = True` in the cell below.)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading...\n", "--2016-02-24 00:28:36--  http://dl.caffe.berkeleyvision.org/caffe_ilsvrc12.tar.gz\n", "Resolving dl.caffe.berkeleyvision.org (dl.caffe.berkeleyvision.org)... ***************\n", "Connecting to dl.caffe.berkeleyvision.org (dl.caffe.berkeleyvision.org)|***************|:80... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 17858008 (17M) [application/octet-stream]\n", "Saving to: ‘caffe_ilsvrc12.tar.gz’\n", "\n", "100%[======================================>] 17,858,008   112MB/s   in 0.2s   \n", "\n", "2016-02-24 00:28:36 (112 MB/s) - ‘caffe_ilsvrc12.tar.gz’ saved [17858008/17858008]\n", "\n", "Unzipping...\n", "Done.\n", "Model already exists.\n", "Downloading 2000 images with 7 workers...\n", "Writing train/val for 1996 successfully downloaded images.\n"]}], "source": ["# Download just a small subset of the data for this exercise.\n", "# (2000 of 80K images, 5 of 20 labels.)\n", "# To download the entire dataset, set `full_dataset = True`.\n", "full_dataset = False\n", "if full_dataset:\n", "    NUM_STYLE_IMAGES = NUM_STYLE_LABELS = -1\n", "else:\n", "    NUM_STYLE_IMAGES = 2000\n", "    NUM_STYLE_LABELS = 5\n", "\n", "# This downloads the ilsvrc auxiliary data (mean file, etc),\n", "# and a subset of 2000 images for the style recognition task.\n", "import os\n", "os.chdir(caffe_root)  # run scripts from caffe root\n", "!data/ilsvrc12/get_ilsvrc_aux.sh\n", "!scripts/download_model_binary.py models/bvlc_reference_caffenet\n", "!python examples/finetune_flickr_style/assemble_data.py \\\n", "    --workers=-1  --seed=1701 \\\n", "    --images=$NUM_STYLE_IMAGES  --label=$NUM_STYLE_LABELS\n", "# back to examples\n", "os.chdir('examples')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define `weights`, the path to the ImageNet pretrained weights we just downloaded, and make sure it exists."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [], "source": ["import os\n", "weights = os.path.join(caffe_root, 'models/bvlc_reference_caffenet/bvlc_reference_caffenet.caffemodel')\n", "assert os.path.exists(weights)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load the 1000 ImageNet labels from `ilsvrc12/synset_words.txt`, and the 5 style labels from `finetune_flickr_style/style_names.txt`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded ImageNet labels:\n", "n01440764 tench, Tinca tinca\n", "n01443537 goldfish, Carassius auratus\n", "n01484850 great white shark, white shark, man-eater, man-eating shark, Carcharodon carcharias\n", "n01491361 tiger shark, <PERSON><PERSON><PERSON><PERSON>\n", "n01494475 hammerhead, hammerhead shark\n", "n01496331 electric ray, crampfish, numbfish, torpedo\n", "n01498041 stingray\n", "n01514668 cock\n", "n01514859 hen\n", "n01518878 ostrich, <PERSON><PERSON><PERSON><PERSON> camelus\n", "...\n", "\n", "Loaded style labels:\n", "Detailed, <PERSON><PERSON>, Melan<PERSON>ly, Noir, HDR\n"]}], "source": ["# Load ImageNet labels to imagenet_labels\n", "imagenet_label_file = caffe_root + 'data/ilsvrc12/synset_words.txt'\n", "imagenet_labels = list(np.loadtxt(imagenet_label_file, str, delimiter='\\t'))\n", "assert len(imagenet_labels) == 1000\n", "print 'Loaded ImageNet labels:\\n', '\\n'.join(imagenet_labels[:10] + ['...'])\n", "\n", "# Load style labels to style_labels\n", "style_label_file = caffe_root + 'examples/finetune_flickr_style/style_names.txt'\n", "style_labels = list(np.loadtxt(style_label_file, str, delimiter='\\n'))\n", "if NUM_STYLE_LABELS > 0:\n", "    style_labels = style_labels[:NUM_STYLE_LABELS]\n", "print '\\nLoaded style labels:\\n', ', '.join(style_labels)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.  Defining and running the nets\n", "\n", "We'll start by defining `caffenet`, a function which initializes the *CaffeNet* architecture (a minor variant on *AlexNet*), taking arguments specifying the data and number of output classes."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": ["from caffe import layers as L\n", "from caffe import params as P\n", "\n", "weight_param = dict(lr_mult=1, decay_mult=1)\n", "bias_param   = dict(lr_mult=2, decay_mult=0)\n", "learned_param = [weight_param, bias_param]\n", "\n", "frozen_param = [dict(lr_mult=0)] * 2\n", "\n", "def conv_relu(bottom, ks, nout, stride=1, pad=0, group=1,\n", "              param=learned_param,\n", "              weight_filler=dict(type='gaussian', std=0.01),\n", "              bias_filler=dict(type='constant', value=0.1)):\n", "    conv = L.Convolution(bottom, kernel_size=ks, stride=stride,\n", "                         num_output=nout, pad=pad, group=group,\n", "                         param=param, weight_filler=weight_filler,\n", "                         bias_filler=bias_filler)\n", "    return conv, <PERSON><PERSON>(conv, in_place=True)\n", "\n", "def fc_relu(bottom, nout, param=learned_param,\n", "            weight_filler=dict(type='gaussian', std=0.005),\n", "            bias_filler=dict(type='constant', value=0.1)):\n", "    fc = L.InnerProduct(bottom, num_output=nout, param=param,\n", "                        weight_filler=weight_filler,\n", "                        bias_filler=bias_filler)\n", "    return fc, <PERSON><PERSON>(fc, in_place=True)\n", "\n", "def max_pool(bottom, ks, stride=1):\n", "    return L.Pooling(bottom, pool=P.Pooling.MAX, kernel_size=ks, stride=stride)\n", "\n", "def caffenet(data, label=None, train=True, num_classes=1000,\n", "             classifier_name='fc8', learn_all=False):\n", "    \"\"\"Returns a NetSpec specifying CaffeNet, following the original proto text\n", "       specification (./models/bvlc_reference_caffenet/train_val.prototxt).\"\"\"\n", "    n = caffe.NetSpec()\n", "    n.data = data\n", "    param = learned_param if learn_all else frozen_param\n", "    n.conv1, n.relu1 = conv_relu(n.data, 11, 96, stride=4, param=param)\n", "    n.pool1 = max_pool(n.relu1, 3, stride=2)\n", "    n.norm1 = L.LRN(n.pool1, local_size=5, alpha=1e-4, beta=0.75)\n", "    n.conv2, n.relu2 = conv_relu(n.norm1, 5, 256, pad=2, group=2, param=param)\n", "    n.pool2 = max_pool(n.relu2, 3, stride=2)\n", "    n.norm2 = L.LRN(n.pool2, local_size=5, alpha=1e-4, beta=0.75)\n", "    n.conv3, n.relu3 = conv_relu(n.norm2, 3, 384, pad=1, param=param)\n", "    n.conv4, n.relu4 = conv_relu(n.relu3, 3, 384, pad=1, group=2, param=param)\n", "    n.conv5, n.relu5 = conv_relu(n.relu4, 3, 256, pad=1, group=2, param=param)\n", "    n.pool5 = max_pool(n.relu5, 3, stride=2)\n", "    n.fc6, n.relu6 = fc_relu(n.pool5, 4096, param=param)\n", "    if train:\n", "        n.drop6 = fc7input = L.Dropout(n.relu6, in_place=True)\n", "    else:\n", "        fc7input = n.relu6\n", "    n.fc7, n.relu7 = fc_relu(fc7input, 4096, param=param)\n", "    if train:\n", "        n.drop7 = fc8input = L.Dropout(n.relu7, in_place=True)\n", "    else:\n", "        fc8input = n.relu7\n", "    # always learn fc8 (param=learned_param)\n", "    fc8 = L.InnerProduct(fc8input, num_output=num_classes, param=learned_param)\n", "    # give fc8 the name specified by argument `classifier_name`\n", "    n.__setattr__(classifier_name, fc8)\n", "    if not train:\n", "        n.probs = <PERSON><PERSON>(fc8)\n", "    if label is not None:\n", "        n.label = label\n", "        n.loss = <PERSON><PERSON>(fc8, n.label)\n", "        n.acc = <PERSON>.Accuracy(fc8, n.label)\n", "    # write the net to a temporary file and return its filename\n", "    with tempfile.NamedTemporaryFile(delete=False) as f:\n", "        f.write(str(n.to_proto()))\n", "        return f.name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's create a *CaffeNet* that takes unlabeled \"dummy data\" as input, allowing us to set its input images externally and see what ImageNet classes it predicts."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [], "source": ["dummy_data = <PERSON><PERSON>Dummy<PERSON>(shape=dict(dim=[1, 3, 227, 227]))\n", "imagenet_net_filename = caffenet(data=dummy_data, train=False)\n", "imagenet_net = caffe.Net(imagenet_net_filename, weights, caffe.TEST)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a function `style_net` which calls `caffenet` on data from the Flickr style dataset.\n", "\n", "The new network will also have the *CaffeNet* architecture, with differences in the input and output:\n", "\n", "- the input is the Flickr style data we downloaded, provided by an `ImageData` layer\n", "- the output is a distribution over 20 classes rather than the original 1000 ImageNet classes\n", "- the classification layer is renamed from `fc8` to `fc8_flickr` to tell Caffe not to load the original classifier (`fc8`) weights from the ImageNet-pretrained model"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [], "source": ["def style_net(train=True, learn_all=False, subset=None):\n", "    if subset is None:\n", "        subset = 'train' if train else 'test'\n", "    source = caffe_root + 'data/flickr_style/%s.txt' % subset\n", "    transform_param = dict(mirror=train, crop_size=227,\n", "        mean_file=caffe_root + 'data/ilsvrc12/imagenet_mean.binaryproto')\n", "    style_data, style_label = L.ImageData(\n", "        transform_param=transform_param, source=source,\n", "        batch_size=50, new_height=256, new_width=256, ntop=2)\n", "    return caffenet(data=style_data, label=style_label, train=train,\n", "                    num_classes=NUM_STYLE_LABELS,\n", "                    classifier_name='fc8_flickr',\n", "                    learn_all=learn_all)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use the `style_net` function defined above to initialize `untrained_style_net`, a *CaffeNet* with input images from the style dataset and weights from the pretrained ImageNet model.\n", "\n", "\n", "Call `forward` on `untrained_style_net` to get a batch of style training data."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [], "source": ["untrained_style_net = caffe.Net(style_net(train=False, subset='train'),\n", "                                weights, caffe.TEST)\n", "untrained_style_net.forward()\n", "style_data_batch = untrained_style_net.blobs['data'].data.copy()\n", "style_label_batch = np.array(untrained_style_net.blobs['label'].data, dtype=np.int32)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pick one of the style net training images from the batch of 50 (we'll arbitrarily choose #8 here).  Display it, then run it through `imagenet_net`, the ImageNet-pretrained network to view its top 5 predicted classes from the 1000 ImageNet classes.\n", "\n", "Below we chose an image where the network's predictions happen to be reasonable, as the image is of a beach, and \"sandbar\" and \"seashore\" both happen to be ImageNet-1000 categories.  For other images, the predictions won't be this good, sometimes due to the network actually failing to recognize the object(s) present in the image, but perhaps even more often due to the fact that not all images contain an object from the (somewhat arbitrarily chosen) 1000 ImageNet categories. Modify the `batch_index` variable by changing its default setting of 8 to another value from 0-49 (since the batch size is 50) to see predictions for other images in the batch.  (To go beyond this batch of 50 images, first rerun the *above* cell to load a fresh batch of data into `style_net`.)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [], "source": ["def disp_preds(net, image, labels, k=5, name='ImageNet'):\n", "    input_blob = net.blobs['data']\n", "    net.blobs['data'].data[0, ...] = image\n", "    probs = net.forward(start='conv1')['probs'][0]\n", "    top_k = (-probs).argsort()[:k]\n", "    print 'top %d predicted %s labels =' % (k, name)\n", "    print '\\n'.join('\\t(%d) %5.2f%% %s' % (i+1, 100*probs[p], labels[p])\n", "                    for i, p in enumerate(top_k))\n", "\n", "def disp_imagenet_preds(net, image):\n", "    disp_preds(net, image, imagenet_labels, name='ImageNet')\n", "\n", "def disp_style_preds(net, image):\n", "    disp_preds(net, image, style_labels, name='style')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["actual label = Melan<PERSON>ly\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAQMAAAEACAYAAAC3RRNlAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f75dc02e4d0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["batch_index = 8\n", "image = style_data_batch[batch_index]\n", "plt.imshow(deprocess_net_image(image))\n", "print 'actual label =', style_labels[style_label_batch[batch_index]]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted ImageNet labels =\n", "\t(1) 69.89% n09421951 sandbar, sand bar\n", "\t(2) 21.76% n09428293 seashore, coast, seacoast, sea-coast\n", "\t(3)  3.22% n02894605 breakwater, groin, groyne, mole, bulwark, seawall, jetty\n", "\t(4)  1.89% n04592741 wing\n", "\t(5)  1.23% n09332890 lakeside, lakeshore\n"]}], "source": ["disp_imagenet_preds(imagenet_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also look at `untrained_style_net`'s predictions, but we won't see anything interesting as its classifier hasn't been trained yet.\n", "\n", "In fact, since we zero-initialized the classifier (see `caffenet` definition -- no `weight_filler` is passed to the final `InnerProduct` layer), the softmax inputs should be all zero and we should therefore see a predicted probability of 1/N for each label (for N labels).  Since we set N = 5, we get a predicted probability of 20% for each class."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted style labels =\n", "\t(1) 20.00% Detailed\n", "\t(2) 20.00% Pastel\n", "\t(3) 20.00% Melancholy\n", "\t(4) 20.00% Noir\n", "\t(5) 20.00% HDR\n"]}], "source": ["disp_style_preds(untrained_style_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also verify that the activations in layer `fc7` immediately before the classification layer are the same as (or very close to) those in the ImageNet-pretrained model, since both models are using the same pretrained weights in the `conv1` through `fc7` layers."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": false}, "outputs": [], "source": ["diff = untrained_style_net.blobs['fc7'].data[0] - imagenet_net.blobs['fc7'].data[0]\n", "error = (diff ** 2).sum()\n", "assert error < 1e-8"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Delete `untrained_style_net` to save memory.  (Hang on to `imagenet_net` as we'll use it again later.)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true}, "outputs": [], "source": ["del untrained_style_net"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Training the style classifier\n", "\n", "Now, we'll define a function `solver` to create our Caffe solvers, which are used to train the network (learn its weights).  In this function we'll set values for various parameters used for learning, display, and \"snapshotting\" -- see the inline comments for explanations of what they mean.  You may want to play with some of the learning parameters to see if you can improve on the results here!"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": false}, "outputs": [], "source": ["from caffe.proto import caffe_pb2\n", "\n", "def solver(train_net_path, test_net_path=None, base_lr=0.001):\n", "    s = caffe_pb2.SolverParameter()\n", "\n", "    # Specify locations of the train and (maybe) test networks.\n", "    s.train_net = train_net_path\n", "    if test_net_path is not None:\n", "        s.test_net.append(test_net_path)\n", "        s.test_interval = 1000  # Test after every 1000 training iterations.\n", "        s.test_iter.append(100) # Test on 100 batches each time we test.\n", "\n", "    # The number of iterations over which to average the gradient.\n", "    # Effectively boosts the training batch size by the given factor, without\n", "    # affecting memory utilization.\n", "    s.iter_size = 1\n", "    \n", "    s.max_iter = 100000     # # of times to update the net (training iterations)\n", "    \n", "    # Solve using the stochastic gradient descent (SGD) algorithm.\n", "    # Other choices include 'Adam' and 'RMSProp'.\n", "    s.type = 'SGD'\n", "\n", "    # Set the initial learning rate for SGD.\n", "    s.base_lr = base_lr\n", "\n", "    # Set `lr_policy` to define how the learning rate changes during training.\n", "    # Here, we 'step' the learning rate by multiplying it by a factor `gamma`\n", "    # every `stepsize` iterations.\n", "    s.lr_policy = 'step'\n", "    s.gamma = 0.1\n", "    s.stepsize = 20000\n", "\n", "    # Set other SGD hyperparameters. Setting a non-zero `momentum` takes a\n", "    # weighted average of the current gradient and previous gradients to make\n", "    # learning more stable. L2 weight decay regularizes learning, to help prevent\n", "    # the model from overfitting.\n", "    s.momentum = 0.9\n", "    s.weight_decay = 5e-4\n", "\n", "    # Display the current training loss and accuracy every 1000 iterations.\n", "    s.display = 1000\n", "\n", "    # Snapshots are files used to store networks we've trained.  Here, we'll\n", "    # snapshot every 10K iterations -- ten times during training.\n", "    s.snapshot = 10000\n", "    s.snapshot_prefix = caffe_root + 'models/finetune_flickr_style/finetune_flickr_style'\n", "    \n", "    # Train on the GPU.  Using the CPU to train large networks is very slow.\n", "    s.solver_mode = caffe_pb2.SolverParameter.GPU\n", "    \n", "    # Write the solver to a temporary file and return its filename.\n", "    with tempfile.NamedTemporaryFile(delete=False) as f:\n", "        f.write(str(s))\n", "        return f.name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we'll invoke the solver to train the style net's classification layer.\n", "\n", "For the record, if you want to train the network using only the command line tool, this is the command:\n", "\n", "<code>\n", "build/tools/caffe train \\\n", "    -solver models/finetune_flickr_style/solver.prototxt \\\n", "    -weights models/bvlc_reference_caffenet/bvlc_reference_caffenet.caffemodel \\\n", "    -gpu 0\n", "</code>\n", "\n", "However, we will train using Python in this example.\n", "\n", "We'll first define `run_solvers`, a function that takes a list of solvers and steps each one in a round robin manner, recording the accuracy and loss values each iteration.  At the end, the learned weights are saved to a file."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": false}, "outputs": [], "source": ["def run_solvers(niter, solvers, disp_interval=10):\n", "    \"\"\"Run solvers for niter iterations,\n", "       returning the loss and accuracy recorded each iteration.\n", "       `solvers` is a list of (name, solver) tuples.\"\"\"\n", "    blobs = ('loss', 'acc')\n", "    loss, acc = ({name: np.zeros(niter) for name, _ in solvers}\n", "                 for _ in blobs)\n", "    for it in range(niter):\n", "        for name, s in solvers:\n", "            s.step(1)  # run a single SGD step in Caffe\n", "            loss[name][it], acc[name][it] = (s.net.blobs[b].data.copy()\n", "                                             for b in blobs)\n", "        if it % disp_interval == 0 or it + 1 == niter:\n", "            loss_disp = '; '.join('%s: loss=%.3f, acc=%2d%%' %\n", "                                  (n, loss[n][it], np.round(100*acc[n][it]))\n", "                                  for n, _ in solvers)\n", "            print '%3d) %s' % (it, loss_disp)     \n", "    # Save the learned weights from both nets.\n", "    weight_dir = tempfile.mkdtemp()\n", "    weights = {}\n", "    for name, s in solvers:\n", "        filename = 'weights.%s.caffemodel' % name\n", "        weights[name] = os.path.join(weight_dir, filename)\n", "        s.net.save(weights[name])\n", "    return loss, acc, weights"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create and run solvers to train nets for the style recognition task.  We'll create two solvers -- one (`style_solver`) will have its train net initialized to the ImageNet-pretrained weights (this is done by the call to the `copy_from` method), and the other (`scratch_style_solver`) will start from a *randomly* initialized net.\n", "\n", "During training, we should see that the ImageNet pretrained net is learning faster and attaining better accuracies than the scratch net."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running solvers for 200 iterations...\n", "  0) pretrained: loss=1.609, acc=28%; scratch: loss=1.609, acc=28%\n", " 10) pretrained: loss=1.293, acc=52%; scratch: loss=1.626, acc=14%\n", " 20) pretrained: loss=1.110, acc=56%; scratch: loss=1.646, acc=10%\n", " 30) pretrained: loss=1.084, acc=60%; scratch: loss=1.616, acc=20%\n", " 40) pretrained: loss=0.898, acc=64%; scratch: loss=1.588, acc=26%\n", " 50) pretrained: loss=1.024, acc=54%; scratch: loss=1.607, acc=32%\n", " 60) pretrained: loss=0.925, acc=66%; scratch: loss=1.616, acc=20%\n", " 70) pretrained: loss=0.861, acc=74%; scratch: loss=1.598, acc=24%\n", " 80) pretrained: loss=0.967, acc=60%; scratch: loss=1.588, acc=30%\n", " 90) pretrained: loss=1.274, acc=52%; scratch: loss=1.608, acc=20%\n", "100) pretrained: loss=1.113, acc=62%; scratch: loss=1.588, acc=30%\n", "110) pretrained: loss=0.922, acc=62%; scratch: loss=1.578, acc=36%\n", "120) pretrained: loss=0.918, acc=62%; scratch: loss=1.599, acc=20%\n", "130) pretrained: loss=0.959, acc=58%; scratch: loss=1.594, acc=22%\n", "140) pretrained: loss=1.228, acc=50%; scratch: loss=1.608, acc=14%\n", "150) pretrained: loss=0.727, acc=76%; scratch: loss=1.623, acc=16%\n", "160) pretrained: loss=1.074, acc=66%; scratch: loss=1.607, acc=20%\n", "170) pretrained: loss=0.887, acc=60%; scratch: loss=1.614, acc=20%\n", "180) pretrained: loss=0.961, acc=62%; scratch: loss=1.614, acc=18%\n", "190) pretrained: loss=0.737, acc=76%; scratch: loss=1.613, acc=18%\n", "199) pretrained: loss=0.836, acc=70%; scratch: loss=1.614, acc=16%\n", "Done.\n"]}], "source": ["niter = 200  # number of iterations to train\n", "\n", "# Reset style_solver as before.\n", "style_solver_filename = solver(style_net(train=True))\n", "style_solver = caffe.get_solver(style_solver_filename)\n", "style_solver.net.copy_from(weights)\n", "\n", "# For reference, we also create a solver that isn't initialized from\n", "# the pretrained ImageNet weights.\n", "scratch_style_solver_filename = solver(style_net(train=True))\n", "scratch_style_solver = caffe.get_solver(scratch_style_solver_filename)\n", "\n", "print 'Running solvers for %d iterations...' % niter\n", "solvers = [('pretrained', style_solver),\n", "           ('scratch', scratch_style_solver)]\n", "loss, acc, weights = run_solvers(niter, solvers)\n", "print 'Done.'\n", "\n", "train_loss, scratch_train_loss = loss['pretrained'], loss['scratch']\n", "train_acc, scratch_train_acc = acc['pretrained'], acc['scratch']\n", "style_weights, scratch_style_weights = weights['pretrained'], weights['scratch']\n", "\n", "# Delete solvers to save memory.\n", "del style_solver, scratch_style_solver, solvers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's look at the training loss and accuracy produced by the two training procedures.  Notice how quickly the ImageNet pretrained model's loss value (blue) drops, and that the randomly initialized model's loss value (green) barely (if at all) improves from training only the classifier layer."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x7f75d49e1090>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAEPCAYAAACp/QjLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ncoiIiIb/AxSD6Sq0YLMCAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f75d496e890>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(np.vstack([train_loss, scratch_train_loss]).T)\n", "xlabel('Iteration #')\n", "ylabel('Loss')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x7f75d49e1a90>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAEPCAYAAACp/QjLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nw9hYshg6MjmCyWlt0xSuedQ/*****************************/5Uv6WuI6/JRJDm60iMJ/IK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\nFwAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f75d47cad90>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot(np.vstack([train_acc, scratch_train_acc]).T)\n", "xlabel('Iteration #')\n", "ylabel('Accuracy')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's take a look at the testing accuracy after running 200 iterations of training. Note that we're classifying among 5 classes, giving chance accuracy of 20%. We expect both results to be better than chance accuracy (20%), and we further expect the result from training using the ImageNet pretraining initialization to be much better than the one from training from scratch. Let's see."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"collapsed": true}, "outputs": [], "source": ["def eval_style_net(weights, test_iters=10):\n", "    test_net = caffe.Net(style_net(train=False), weights, caffe.TEST)\n", "    accuracy = 0\n", "    for it in xrange(test_iters):\n", "        accuracy += test_net.forward()['acc']\n", "    accuracy /= test_iters\n", "    return test_net, accuracy"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy, trained from ImageNet initialization: 50.0%\n", "Accuracy, trained from   random initialization: 23.6%\n"]}], "source": ["test_net, accuracy = eval_style_net(style_weights)\n", "print 'Accuracy, trained from ImageNet initialization: %3.1f%%' % (100*accuracy, )\n", "scratch_test_net, scratch_accuracy = eval_style_net(scratch_style_weights)\n", "print 'Accuracy, trained from   random initialization: %3.1f%%' % (100*scratch_accuracy, )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. End-to-end finetuning for style\n", "\n", "Finally, we'll train both nets again, starting from the weights we just learned.  The only difference this time is that we'll be learning the weights \"end-to-end\" by turning on learning in *all* layers of the network, starting from the RGB `conv1` filters directly applied to the input image.  We pass the argument `learn_all=True` to the `style_net` function defined earlier in this notebook, which tells the function to apply a positive (non-zero) `lr_mult` value for all parameters.  Under the default, `learn_all=False`, all parameters in the pretrained layers (`conv1` through `fc7`) are frozen (`lr_mult = 0`), and we learn only the classifier layer `fc8_flickr`.\n", "\n", "Note that both networks start at roughly the accuracy achieved at the end of the previous training session, and improve significantly with end-to-end training.  To be more scientific, we'd also want to follow the same additional training procedure *without* the end-to-end training, to ensure that our results aren't better simply because we trained for twice as long.  Feel free to try this yourself!"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running solvers for 200 iterations...\n", "  0) pretrained, end-to-end: loss=0.781, acc=64%; scratch, end-to-end: loss=1.585, acc=28%\n", " 10) pretrained, end-to-end: loss=1.178, acc=62%; scratch, end-to-end: loss=1.638, acc=14%\n", " 20) pretrained, end-to-end: loss=1.084, acc=60%; scratch, end-to-end: loss=1.637, acc= 8%\n", " 30) pretrained, end-to-end: loss=0.902, acc=76%; scratch, end-to-end: loss=1.600, acc=20%\n", " 40) pretrained, end-to-end: loss=0.865, acc=64%; scratch, end-to-end: loss=1.574, acc=26%\n", " 50) pretrained, end-to-end: loss=0.888, acc=60%; scratch, end-to-end: loss=1.604, acc=26%\n", " 60) pretrained, end-to-end: loss=0.538, acc=78%; scratch, end-to-end: loss=1.555, acc=34%\n", " 70) pretrained, end-to-end: loss=0.717, acc=72%; scratch, end-to-end: loss=1.563, acc=30%\n", " 80) pretrained, end-to-end: loss=0.695, acc=74%; scratch, end-to-end: loss=1.502, acc=42%\n", " 90) pretrained, end-to-end: loss=0.708, acc=68%; scratch, end-to-end: loss=1.523, acc=26%\n", "100) pretrained, end-to-end: loss=0.432, acc=78%; scratch, end-to-end: loss=1.500, acc=38%\n", "110) pretrained, end-to-end: loss=0.611, acc=78%; scratch, end-to-end: loss=1.618, acc=18%\n", "120) pretrained, end-to-end: loss=0.610, acc=76%; scratch, end-to-end: loss=1.473, acc=30%\n", "130) pretrained, end-to-end: loss=0.471, acc=78%; scratch, end-to-end: loss=1.488, acc=26%\n", "140) pretrained, end-to-end: loss=0.500, acc=76%; scratch, end-to-end: loss=1.514, acc=38%\n", "150) pretrained, end-to-end: loss=0.476, acc=80%; scratch, end-to-end: loss=1.452, acc=46%\n", "160) pretrained, end-to-end: loss=0.368, acc=82%; scratch, end-to-end: loss=1.419, acc=34%\n", "170) pretrained, end-to-end: loss=0.556, acc=76%; scratch, end-to-end: loss=1.583, acc=36%\n", "180) pretrained, end-to-end: loss=0.574, acc=72%; scratch, end-to-end: loss=1.556, acc=22%\n", "190) pretrained, end-to-end: loss=0.360, acc=88%; scratch, end-to-end: loss=1.429, acc=44%\n", "199) pretrained, end-to-end: loss=0.458, acc=78%; scratch, end-to-end: loss=1.370, acc=44%\n", "Done.\n"]}], "source": ["end_to_end_net = style_net(train=True, learn_all=True)\n", "\n", "# Set base_lr to 1e-3, the same as last time when learning only the classifier.\n", "# You may want to play around with different values of this or other\n", "# optimization parameters when fine-tuning.  For example, if learning diverges\n", "# (e.g., the loss gets very large or goes to infinity/NaN), you should try\n", "# decreasing base_lr (e.g., to 1e-4, then 1e-5, etc., until you find a value\n", "# for which learning does not diverge).\n", "base_lr = 0.001\n", "\n", "style_solver_filename = solver(end_to_end_net, base_lr=base_lr)\n", "style_solver = caffe.get_solver(style_solver_filename)\n", "style_solver.net.copy_from(style_weights)\n", "\n", "scratch_style_solver_filename = solver(end_to_end_net, base_lr=base_lr)\n", "scratch_style_solver = caffe.get_solver(scratch_style_solver_filename)\n", "scratch_style_solver.net.copy_from(scratch_style_weights)\n", "\n", "print 'Running solvers for %d iterations...' % niter\n", "solvers = [('pretrained, end-to-end', style_solver),\n", "           ('scratch, end-to-end', scratch_style_solver)]\n", "_, _, finetuned_weights = run_solvers(niter, solvers)\n", "print 'Done.'\n", "\n", "style_weights_ft = finetuned_weights['pretrained, end-to-end']\n", "scratch_style_weights_ft = finetuned_weights['scratch, end-to-end']\n", "\n", "# Delete solvers to save memory.\n", "del style_solver, scratch_style_solver, solvers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now test the end-to-end finetuned models.  Since all layers have been optimized for the style recognition task at hand, we expect both nets to get better results than the ones above, which were achieved by nets with only their classifier layers trained for the style task (on top of either ImageNet pretrained or randomly initialized weights)."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy, finetuned from ImageNet initialization: 53.6%\n", "Accuracy, finetuned from   random initialization: 39.2%\n"]}], "source": ["test_net, accuracy = eval_style_net(style_weights_ft)\n", "print 'Accuracy, finetuned from ImageNet initialization: %3.1f%%' % (100*accuracy, )\n", "scratch_test_net, scratch_accuracy = eval_style_net(scratch_style_weights_ft)\n", "print 'Accuracy, finetuned from   random initialization: %3.1f%%' % (100*scratch_accuracy, )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll first look back at the image we started with and check our end-to-end trained model's predictions."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted style labels =\n", "\t(1) 55.67% Melancholy\n", "\t(2) 27.21% HDR\n", "\t(3) 16.46% Pastel\n", "\t(4)  0.63% Detailed\n", "\t(5)  0.03% Noir\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAQMAAAEACAYAAAC3RRNlAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f75d0683e10>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(deprocess_net_image(image))\n", "disp_style_preds(test_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Whew, that looks a lot better than before!  But note that this image was from the training set, so the net got to see its label at training time.\n", "\n", "Finally, we'll pick an image from the test set (an image the model hasn't seen) and look at our end-to-end finetuned style model's predictions for it."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["actual label = Pastel\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAQMAAAEACAYAAAC3RRNlAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f75d05ad810>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["batch_index = 1\n", "image = test_net.blobs['data'].data[batch_index]\n", "plt.imshow(deprocess_net_image(image))\n", "print 'actual label =', style_labels[int(test_net.blobs['label'].data[batch_index])]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted style labels =\n", "\t(1) 99.76% Pastel\n", "\t(2)  0.13% HDR\n", "\t(3)  0.11% Detailed\n", "\t(4)  0.00% Melancholy\n", "\t(5)  0.00% Noir\n"]}], "source": ["disp_style_preds(test_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also look at the predictions of the network trained from scratch.  We see that in this case, the scratch network also predicts the correct label for the image (*Pastel*), but is much less confident in its prediction than the pretrained net."]}, {"cell_type": "code", "execution_count": 27, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted style labels =\n", "\t(1) 49.81% Pastel\n", "\t(2) 19.76% Detailed\n", "\t(3) 17.06% Melancholy\n", "\t(4) 11.66% HDR\n", "\t(5)  1.72% Noir\n"]}], "source": ["disp_style_preds(scratch_test_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Of course, we can again look at the ImageNet model's predictions for the above image:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top 5 predicted ImageNet labels =\n", "\t(1) 34.90% n07579787 plate\n", "\t(2) 21.63% n04263257 soup bowl\n", "\t(3) 17.75% n07875152 potpie\n", "\t(4)  5.72% n07711569 mashed potato\n", "\t(5)  5.27% n07584110 consomme\n"]}], "source": ["disp_imagenet_preds(imagenet_net, image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So we did finetuning and it is awesome. Let's take a look at what kind of results we are able to get with a longer, more complete run of the style recognition dataset. Note: the below URL might be occasionally down because it is run on a research machine.\n", "\n", "http://demo.vislab.berkeleyvision.org/"]}], "metadata": {"description": "Fine-tune the ImageNet-trained CaffeNet on new data.", "example_name": "Fine-tuning for Style Recognition", "include_in_docs": true, "kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.10"}, "priority": 3}, "nbformat": 4, "nbformat_minor": 0}