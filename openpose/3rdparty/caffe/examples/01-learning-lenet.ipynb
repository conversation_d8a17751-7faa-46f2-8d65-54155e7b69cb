{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solving in Python with LeNet\n", "\n", "In this example, we'll explore learning with <PERSON><PERSON><PERSON> in Python, using the fully-exposed `Solver` interface."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Setup"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Set up the Python environment: we'll use the `pylab` import for numpy and plot inline."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["from pylab import *\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Import `caffe`, adding it to `sys.path` if needed. Make sure you've built pycaffe."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [], "source": ["caffe_root = '../'  # this file should be run from {caffe_root}/examples (otherwise change this line)\n", "\n", "import sys\n", "sys.path.insert(0, caffe_root + 'python')\n", "import caffe"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* We'll be using the provided LeNet example data and networks (make sure you've downloaded the data and created the databases, as below)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading...\n", "Creating lmdb...\n", "Done.\n"]}], "source": ["# run scripts from caffe root\n", "import os\n", "os.chdir(caffe_root)\n", "# Download data\n", "!data/mnist/get_mnist.sh\n", "# Prepare data\n", "!examples/mnist/create_mnist.sh\n", "# back to examples\n", "os.chdir('examples')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. <PERSON><PERSON>ting the net \n", "\n", "Now let's make a variant of LeNet, the classic 1989 convnet architecture.\n", "\n", "We'll need two external files to help out:\n", "* the net `prototxt`, defining the architecture and pointing to the train/test data\n", "* the solver `prototxt`, defining the learning parameters\n", "\n", "We start by creating the net. We'll write the net in a succinct and natural way as Python code that serializes to Caffe's protobuf model format.\n", "\n", "This network expects to read from pregenerated LMDBs, but reading directly from `ndarray`s is also possible using `MemoryDataLayer`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [], "source": ["from caffe import layers as L, params as P\n", "\n", "def lenet(lmdb, batch_size):\n", "    # our version of LeNet: a series of linear and simple nonlinear transformations\n", "    n = caffe.NetSpec()\n", "    \n", "    n.data, n.label = L.Data(batch_size=batch_size, backend=P.Data.LMDB, source=lmdb,\n", "                             transform_param=dict(scale=1./255), ntop=2)\n", "    \n", "    n.conv1 = L.Convolution(n.data, kernel_size=5, num_output=20, weight_filler=dict(type='xavier'))\n", "    n.pool1 = <PERSON>.Pooling(n.conv1, kernel_size=2, stride=2, pool=P.Pooling.MAX)\n", "    n.conv2 = L.Convolution(n.pool1, kernel_size=5, num_output=50, weight_filler=dict(type='xavier'))\n", "    n.pool2 = <PERSON>.Pooling(n.conv2, kernel_size=2, stride=2, pool=P.Pooling.MAX)\n", "    n.fc1 =   <PERSON>.InnerProduct(n.pool2, num_output=500, weight_filler=dict(type='xavier'))\n", "    n.relu1 = L.ReLU(n.fc1, in_place=True)\n", "    n.score = <PERSON>.InnerProduct(n.relu1, num_output=10, weight_filler=dict(type='xavier'))\n", "    n.loss =  <PERSON><PERSON>(n.score, n.label)\n", "    \n", "    return n.to_proto()\n", "    \n", "with open('mnist/lenet_auto_train.prototxt', 'w') as f:\n", "    f.write(str(lenet('mnist/mnist_train_lmdb', 64)))\n", "    \n", "with open('mnist/lenet_auto_test.prototxt', 'w') as f:\n", "    f.write(str(lenet('mnist/mnist_test_lmdb', 100)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The net has been written to disk in a more verbose but human-readable serialization format using Google's protobuf library. You can read, write, and modify this description directly. Let's take a look at the train net."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layer {\r\n", "  name: \"data\"\r\n", "  type: \"Data\"\r\n", "  top: \"data\"\r\n", "  top: \"label\"\r\n", "  transform_param {\r\n", "    scale: 0.00392156862745\r\n", "  }\r\n", "  data_param {\r\n", "    source: \"mnist/mnist_train_lmdb\"\r\n", "    batch_size: 64\r\n", "    backend: LMDB\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"conv1\"\r\n", "  type: \"Convolution\"\r\n", "  bottom: \"data\"\r\n", "  top: \"conv1\"\r\n", "  convolution_param {\r\n", "    num_output: 20\r\n", "    kernel_size: 5\r\n", "    weight_filler {\r\n", "      type: \"xavier\"\r\n", "    }\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"pool1\"\r\n", "  type: \"Pooling\"\r\n", "  bottom: \"conv1\"\r\n", "  top: \"pool1\"\r\n", "  pooling_param {\r\n", "    pool: MAX\r\n", "    kernel_size: 2\r\n", "    stride: 2\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"conv2\"\r\n", "  type: \"Convolution\"\r\n", "  bottom: \"pool1\"\r\n", "  top: \"conv2\"\r\n", "  convolution_param {\r\n", "    num_output: 50\r\n", "    kernel_size: 5\r\n", "    weight_filler {\r\n", "      type: \"xavier\"\r\n", "    }\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"pool2\"\r\n", "  type: \"Pooling\"\r\n", "  bottom: \"conv2\"\r\n", "  top: \"pool2\"\r\n", "  pooling_param {\r\n", "    pool: MAX\r\n", "    kernel_size: 2\r\n", "    stride: 2\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"fc1\"\r\n", "  type: \"InnerProduct\"\r\n", "  bottom: \"pool2\"\r\n", "  top: \"fc1\"\r\n", "  inner_product_param {\r\n", "    num_output: 500\r\n", "    weight_filler {\r\n", "      type: \"xavier\"\r\n", "    }\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"relu1\"\r\n", "  type: \"ReLU\"\r\n", "  bottom: \"fc1\"\r\n", "  top: \"fc1\"\r\n", "}\r\n", "layer {\r\n", "  name: \"score\"\r\n", "  type: \"InnerProduct\"\r\n", "  bottom: \"fc1\"\r\n", "  top: \"score\"\r\n", "  inner_product_param {\r\n", "    num_output: 10\r\n", "    weight_filler {\r\n", "      type: \"xavier\"\r\n", "    }\r\n", "  }\r\n", "}\r\n", "layer {\r\n", "  name: \"loss\"\r\n", "  type: \"SoftmaxWithLoss\"\r\n", "  bottom: \"score\"\r\n", "  bottom: \"label\"\r\n", "  top: \"loss\"\r\n", "}\r\n"]}], "source": ["!cat mnist/lenet_auto_train.prototxt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's see the learning parameters, which are also written as a `prototxt` file (already provided on disk). We're using SGD with momentum, weight decay, and a specific learning rate schedule."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# The train/test net protocol buffer definition\r\n", "train_net: \"mnist/lenet_auto_train.prototxt\"\r\n", "test_net: \"mnist/lenet_auto_test.prototxt\"\r\n", "# test_iter specifies how many forward passes the test should carry out.\r\n", "# In the case of MNIST, we have test batch size 100 and 100 test iterations,\r\n", "# covering the full 10,000 testing images.\r\n", "test_iter: 100\r\n", "# Carry out testing every 500 training iterations.\r\n", "test_interval: 500\r\n", "# The base learning rate, momentum and the weight decay of the network.\r\n", "base_lr: 0.01\r\n", "momentum: 0.9\r\n", "weight_decay: 0.0005\r\n", "# The learning rate policy\r\n", "lr_policy: \"inv\"\r\n", "gamma: 0.0001\r\n", "power: 0.75\r\n", "# Display every 100 iterations\r\n", "display: 100\r\n", "# The maximum number of iterations\r\n", "max_iter: 10000\r\n", "# snapshot intermediate results\r\n", "snapshot: 5000\r\n", "snapshot_prefix: \"mnist/lenet\"\r\n"]}], "source": ["!cat mnist/lenet_auto_solver.prototxt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Loading and checking the solver\n", "\n", "* Let's pick a device and load the solver. We'll use SGD (with momentum), but other methods (such as Adagrad and <PERSON><PERSON><PERSON>'s accelerated gradient) are also available."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [], "source": ["caffe.set_device(0)\n", "caffe.set_mode_gpu()\n", "\n", "### load the solver and create train and test nets\n", "solver = None  # ignore this workaround for lmdb data (can't instantiate two solvers on the same data)\n", "solver = caffe.SGDSolver('mnist/lenet_auto_solver.prototxt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* To get an idea of the architecture of our net, we can check the dimensions of the intermediate features (blobs) and parameters (these will also be useful to refer to when manipulating data later)."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"text/plain": ["[('data', (64, 1, 28, 28)),\n", " ('label', (64,)),\n", " ('conv1', (64, 20, 24, 24)),\n", " ('pool1', (64, 20, 12, 12)),\n", " ('conv2', (64, 50, 8, 8)),\n", " ('pool2', (64, 50, 4, 4)),\n", " ('fc1', (64, 500)),\n", " ('score', (64, 10)),\n", " ('loss', ())]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# each output is (batch size, feature dim, spatial dim)\n", "[(k, v.data.shape) for k, v in solver.net.blobs.items()]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[('conv1', (20, 1, 5, 5)),\n", " ('conv2', (50, 20, 5, 5)),\n", " ('fc1', (500, 800)),\n", " ('score', (10, 500))]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# just print the weight sizes (we'll omit the biases)\n", "[(k, v[0].data.shape) for k, v in solver.net.params.items()]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Before taking off, let's check that everything is loaded as we expect. We'll run a forward pass on the train and test nets and check that they contain our data."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["{'loss': array(2.365971088409424, dtype=float32)}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["solver.net.forward()  # train net\n", "solver.test_nets[0].forward()  # test net (there can be more than one)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["train labels: [ 5.  0.  4.  1.  9.  2.  1.  3.]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAWwAAABKCAYAAACfHW4mAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAIABJREFUeJztndlXW1ea9n+S0CwBkpAQCMw8jwZscOzYwWMcO6mVpFJd\nXV2u7rrpm/4D+rb/g77o1b1WX1X1Rd9UkkplqtixcUxsPGCbMWDMPAo0MWhAE6DvIuucgsQZbCMB\n/Z3fWtzIWGwdnf2cvd/3ed8tSyQSSEhISEjsf+R7PQAJCQkJiZ+HJNgSEhISBwRJsCUkJCQOCJJg\nS0hISBwQJMGWkJCQOCBIgi0hISFxQJAEW0JCQuKAIAm2hISExAFBEmwJCQmJA4Ik2BISEhIHhLRk\nvbFMJpNq3iUkJCRegEQiIXvW69IKW0JCQuKAIAm2hISExAFBEmwJCQmJA0LSYtj/vyOXy1EoFKhU\nKpRKJQqFgng8TiwWIx6Ps7W1hdTaVkLib8jlctRqNWq1GoVCwdbWFpFIhGg0ytbW1l4Pb18gCXaS\nMJlMFBYW0tLSQmNjIzabjYGBAR4/fszAwABLS0vEYrG9HqaExL4hMzOTCxcucOzYMUpKSnC73Vy7\ndo3r16+ztrbGxsbGXg9xz9n3gi2TyVAoFCgUCvE1tVqNw+FAq9WSlpaGTqfD6XTi8/koKSkhNzeX\nzMxMtra2cLlczMzMMD09TSQSSfp4FQoFBoOB5uZmXnvtNRobG6mursZisVBQUMChQ4fIycnho48+\nwuv1Jn08L0NaWhomk4nS0lIsFgu9vb14vV6i0eheDw2NRkNBQQFFRUXY7XZkMhljY2OMjIywuroq\nTe6fQC6Xk5aWhkKhICMjg4yMDFQqFR6PB7fbnfIVrUqlwmazcerUKdrb2ykqKsLn8+Fyuejp6SEU\nCu2L71StVqPRaJDJZBgMBqxWK+Xl5Wi12h/8P/F4HK/Xy9jYGNPT0y91bfeNYMtkf3OxyOVyZDKZ\nKNZarRaNRiP+u9ls5vz589hsNvR6PTk5Ody4cYPe3l5++9vfcvr0aaqqqojFYnz99dd89NFHfPjh\nh0kXbJlMhlqt5tChQ7z55pv87ne/Q6vVIpd/mypoaGigsrKSqqoqbt++jc/ne+mwiPDewK5PMo1G\nQ2lpKVeuXKGxsZF/+7d/4/Hjx3su2HK5nMzMTM6ePcsvf/lLTp48iVwu5w9/+AP/9V//xfDw8L6Y\n3PsNYU7J5XI0Gg1arRatVktFRQUVFRWkp6fT3d1NV1dXysMQOp2O3NxcmpqacDgcKBQKrFYrOTk5\nWK1WnE5nysbyXbZfN5PJhNVqRaFQUFBQQGtrK7///e/Jzs7+wbkcCAR4/Pgxf/zjH1lcXHypa7sv\nBFuhUJCeno5KpUKj0eBwOHA4HNhsNjQaDUVFRRQUFIi/r1Qqyc7ORq1WI5PJiMfjbGxsUFlZyalT\np7Db7QQCAVwuF6Ojo0xOTqZEZMxmM5WVlfz93/89J0+eFJ/EGxsbxGIxtra2xM8qrCCWl5dfOJ6t\nVqvJy8sjPT2dzc1NxsbGCIfDu/Z5lEol+fn5AMzNzRGPx3ftvV+GgoIC2trauHTpEuXl5SQSCSkn\n8DPQ6XTk5ORw9OhRSkpKcDgcZGdnYzKZMBqNpKWlUVxcjMlk4saNG6ysrKRsbNFoFJ/Px/j4ODab\nTbzv9gPp6enk5ORQW1tLfX09paWlyOVyLBYLDoeDjIwMEonED95/Go2G6upqLl26BEBHRwcej+eF\n5tOeC7ZGo8Fms9Ha2orFYhFXzIJgq9Vq8vPzycvLe+b/DwQC9Pf3E4vF0Gg0uN1uPB4Pfr+fxcVF\nuru7kyrYwtZSr9fT0NBAe3s7Z86cIT8/n7S0NBKJBOFwGK/Xy/z8PA6HQ9whaLVaBgcHmZ2dfaHx\nqdVqMQQUi8WYn5/fVcFWqVTY7XZ0Oh3xeJxEIrFjJ7RXWK1WqqqqqKqqwmKx7Nm4hPCXxWLBbDaT\nnp6OXq9HrVYD4HK5WFxcJBaLodVqUSqV4r25m9/Tj6FWqzEajdjtdgoLC6msrKS1tZWioiJsNhtG\no1HcpQkhxnA4zODgIOvr6ynbTcXjcQKBAAsLC6ytre0rwc7Pz+f48eMcP36cmpoacWzCDkUmk/3o\nYiEtLQ2r1UpLSwuxWIzR0VGCweDBFOzMzEwOHz7Mv/7rv1JeXi6GPoQJ+GMTMZFIsLa2xtWrV5me\nniYWi3Hjxg2CwSBra2sEg0FcLhderzdpqy9h0hYVFXHp0iXee+89rFYrSqVS/J1gMMj4+DiffPIJ\n7e3tvPPOO/zLv/wLVVVVvP/++3z88ccvLNjFxcVUVlYSDofp7Ozctc8lk8lIS0vDbDZjtVqBb2+8\n/YBOp8NkMolugr1CpVJx6NAhWltbaW1tpbq6moKCAvF6Xb9+nS+++IKVlRVycnJIT0/nzp07DA8P\ns7CwkJIxGo1GysvLeeONNzh16hQNDQ2o1Wrkcjmbm5v4fD5CoRAADoeDvLw8WlpaKCgoSGm+YmNj\ng/X1dZaXl1lfX0/J3/y51NXV8d5779HU1ITRaHzhxUFBQQFbW1tcvXoVp9OJ3+9/7vfY8xkYDodZ\nWVlhfX1dDBk8i1gshsvlIhQKIZfLcTgcKJVKVldXefjwIePj42xubgKIIYiNjQ0ikUhSt8qHDx/m\n9OnTNDU1UV1djdlsRqFQ7PhSMzMzcTgc6HQ6lpeXmZqawuFwYDKZyM3N3SHuz4NOp6O5uRmLxcLo\n6OhufSTg252P3W7n2LFjGI1GBgYGcDqdBAKBXf07z4Ow22pra+P48eMYDAYCgQDT09N89tln3L59\nO2Whm7y8POrr67l06RLV1dU4HA4yMzMJh8NMTEyg1WopKiriypUr4gp7c3MTg8FALBZLqmArlUos\nFguvvPIKTU1N1NTUiMlZQazX19eZmpriT3/6E263m5ycHP75n/8Zq9WK0WjEaDSiUqmSNsZnjTk9\nPZ3c3FwyMjJS9nd/Dh6Ph8nJSerq6pDJZGxtbbG+vs7k5CQzMzPiXBd0xmKxkJOTQ25urrjbAsQ4\n+MvsBveFYHs8HoaHh9FqtWRlZREOh3es7KLRKC6Xi87OTlwuFwqFgoqKCux2Oy6Xi6mpKaanp1M6\nbrlcjk6no76+nsuXL1NXV4der2dzc5NAIEAgEGBjY4Pc3FwxNh+LxZienmZwcBCLxYJWqyU9PX1H\n4vB5UKlU5Ofn70jI7hYOh4Pm5mYqKipwu904nU5WVlb2LOGo1Wqx2+2cPHmSY8eOUVpailqtZn5+\nnu7ubv785z8zOTlJOBxOWsJRSIIL3/ulS5e4cOECmZmZRCIR5ubmGBsbY25uDoPBQHV1NdXV1aSn\np5OWlsbKygqzs7MYjcakjE8gPT2d8vJyLl26RGtrK4WFhWK+Z2tri2g0ytOnT+ns7OTTTz9ldXWV\nmpoafvvb35KWloZGo0Gj0aR0RyWEFbOzszEYDOLrarWa9PR0dDodkUhkT5LJ8/Pz3Lt3j8zMTMxm\nM1tbWwSDQYaHhxkdHf1eSCQ7O5vKykrOnz+P1WoVr2MoFMLtdrO6uvrC82jPBTsej7O4uMgnn3yC\n0+kkLy+P2dlZXn31Vc6ePQvA6uoqAwMD/Od//ifj4+PI5XLy8/M5ceIEOTk5KYsHbkelUpGXlyfG\nUoVYViwWY2pqioGBAfx+P3/3d3+HTqfD5/MxOztLKBQiFotx5MgRMfv8Mk/cZMVum5ubee+99zCZ\nTExOTuL1evfUeWGxWGhqauLKlSvU1NSICd3R0VE6OjqYnZ0lEAgkdTclk8lEO+G5c+f41a9+hdFo\nxOl00tfXxyeffEJPTw/z8/MolUreffdd/uEf/oHa2loMBgPxeJy5ubmkJ/McDgetra2cOnWKvLy8\nHTu+jY0NfD4fX3zxBf/zP/+D0+lEr9cndTw/h7S0NLRaLSaTCZ1OJ75uNpspLCxkbm6OSCTyQmGE\nl2ViYoKlpSW6urpQKpUkEgk2NjZYXV195ni0Wi3V1dWUlZVhNBrFB9DCwgK9vb2MjY298D2w54Kd\nSCRYX1+nv78fj8dDRkYGPp+PjY0NLBYLFRUVjI+P09nZKSYkBOdFNBrFYDCwurqa0jGnp6dTVlbG\nr3/9a06dOoVOp0Mmk+F2uxkaGuLDDz9kZWUFk8nEl19+icfjobe3l4GBAaLRKHK5nHA4jM1mE+Oe\ngUCAtbW15xpDTk6O6DffbXQ6HRkZGSgUCtxuN+Pj43tq5zt06BBHjx6loKAAo9HIxsYGi4uL9PT0\n8PDhQ4LBYNJdIg6Hg8bGRi5fvsyxY8dQq9XMzMxw/fp1rl69ysjIiOhhLiwspLS0lIKCAlQqFeFw\nmKWlJR49esTc3FxSx5mXl0dDQwMZGRmkpaWJuz6fz8fMzAy3bt2is7NTLN6y2WxkZWXtaY4iEong\ndrvp6+vDZrNhMpkAqKys5I033iASiRCLxfZEsDc2NggEAsRiMXE3vLW1RTwef2b4LSsrC4fDgcFg\n2HFNQ6EQPp+P9fX1F1787LlgA2JMz+12k5aWRjQaFSdHYWEhoVBITIAI4rS2tvZcArcbyOVyMQxx\n5MgRLl++zKFDh0QnyNTUFPfu3ePq1avAtyLj8/mYnp7myZMnrK+vs7m5iclkIhqNkpmZSUlJCfX1\n9Xi93uf6PGazmaKiIjIzMwkGg7v2GYVko06nw2AwIJfLWVlZYW5ubk8qM4UwRHFxMUePHsVsNovh\nhf7+fvr7+5MeDlMqldhsNo4ePcqFCxf4xS9+gUqlYnZ2lvv373P16lU6OjrE+zMrK4uWlhbq6uqw\n2+0AOJ1O+vv7GRwcxO12J3W8QtjD6XTidrvx+/243W7m5+cZGRnh+vXrzM3Nick9wemylwlcobjk\nzp07lJaWUltbC0Bubi5yuZz+/n6Ghob2ZGyJRILNzc2fTIampaWJ4aiGhgZMJtOO/JTH42F8fJz1\n9fUXXlzsC8EW2NjYYGNjg0QiQTweFxOGRUVFtLa20tnZyfLy8p75bdPS0sjKyqKpqYn29naysrLE\neNba2hqDg4M8evSI1dVVgsEgS0tLKBSKHT5s+FtyQqFQYDKZeO2115ibm+Pp06c/eyx2u52qqiqM\nRuOuhoTkcjkGgwGz2SyKYzgcxu/3i0ndVCKXy9Hr9VRUVHDkyBFUKhXRaJSlpSWuX7/OyMhI0seQ\nkZEhCvXJkyfR6XQMDg7S0dHBBx98wPj4+I7kdnp6OqdPn6ayslJ8j76+Pj766COcTmfSdyo9PT0E\ng0Gmp6dZW1tjbGyMpaUllpeXWVtb+94KT61WYzAY9lSwAfx+Pzdu3KC1tZW33nprT8fyImi1Whob\nG8U8S2Zm5o5rOjQ0xLVr114qcb+vBHu7EI+Pj/PVV19RXFxMXl4etbW1lJeXEwwG8fl8KR/b9uq6\ns2fPcvjwYZRKJX19fdy7dw+n08nU1BSTk5NEIpEf3C4JCDFFweucnp7+XOPJyMjAbrejUqkIBoO7\nFmMWxKahoQGdTifuZMLhcMoflFqtltzcXM6dO0dbWxtarZatrS1mZmbo6uri8ePHLC0tJXUMxcXF\ntLW18dZbb9HQ0EBaWhrT09N0dnby2WefMT4+Lm7T09LSsFgsVFZWUlhYSEZGBqFQiMHBQTo7O+nt\n7U1J6GZ1dZWRkRHC4TDhcJjl5WVCoZB4X34Xk8lEXl7eC7uVdoutrS0xx7Mf/P4/B6Gwr6qqipqa\nGiorKykuLt5hBtjY2GB5eVn04L/MwmdfCfZ2ZmZmSCQSVFZWcuHCBQoLC2lra2Nzc1OMpwaDQdFD\nmmwEYT179iyvvPIKNptNjAd+9NFHzM/Pi1/ET9nKhKyyYPPZXmzxc9FqtWRkZCCXy1lbW2NxcfGF\n7GxCCESr1Yp+8osXL1JXV4dcLmdqauqly2lflPT0dCoqKnjnnXdoaGgQt6XDw8PcunWLsbGxpIXF\nhO+mqqqKS5cucfz4cTQaDYuLi9y5c4eOjg4ePHhAPB5HoVBgNBrJycmhsrKStrY27HY7iUQCp9PJ\n9evXuXfvHrOzs0kZ63eJxWJ4vd7v9arR6/WYTKbvJbrz8/PJz89HpVIRi8UIBoMEg8F905xsr8Vb\n6LppMBjQ6XQ75mp6ejrHjh3j5MmTHDlyBIvFIlont7a2WF1dFXNAs7OzbG5uvtQDe98K9ubmJi6X\ni/fffx+TycQvf/lL3n33XWpqahgZGWF2dpZHjx4xODiYkpWfEG8W4pIej4c//vGP3Lx5k5GRkR03\n9089QYXx7ta4V1ZWmJ+ff6EJplQqyczMpKqqisOHD9PS0sKxY8fIzs4Wt6gDAwOEw+GUC7bdbqe2\ntpaioiLS09OJRqOMj49z9+5d7ty5s6ux++8iOEIqKys5duwYer2ehYUF7t69y3//938zMjIiVn8K\nIZvf/e53NDU1UVBQgNlsFncCX3zxxa775F+E0tJSsXBmuwg2NDSQk5ODUqnE5/MxNjbG2NgYy8vL\nezbW7aXee91yQK/Xk5uby5EjR6iqqtpRhanX66mpqSErKwudTodCoRDFOhKJ0N3dzY0bN+jv7+fp\n06fiPfOi7FvBTiQSRCIRJiYm+Prrr7HZbDQ0NNDc3ExxcTHLy8tYrVZUKhXT09P4/f6krggqKys5\nc+YM2dnZhMNhxsfH6e7uZmJi4rkrs7bb+ZK5ejAYDOj1emQyGSaTSfR+CyvqQ4cOYTAY0Gq1WK1W\nMjIyxMIJpVJJJBJhdHQUl8uVUrFWq9Xk5uZy/Phxzpw5g8ViIRQKMTk5yccff8zdu3fxeDxJtRnK\nZDLxYWaz2UhLS2N4eJirV68yPz8vlhoLbRRKSko4fvw4DodD9FnPzc2JrpBkPlx+aPwKhQK9Xo/V\naqW6uppjx46JYZ3t911WVhY2m414PE5PTw+ff/45Lpdrz3rH7LVACwhJxLa2Nk6dOkVVVZVY8Cag\nVCp3eK0F4vE4CwsLPHz4kOvXr7O4uLgrttN9K9jw7Up1bW2NBw8eiE2TKioqqKmpEbcoaWlp3Lp1\ni4mJCTwez643ARKaqtfV1dHe3o7RaGR2dpYnT54wOTn5wpZCQayFBOvzis/2FUhmZiaFhYV4vd4d\nwpCdnS26FPLz8ykqKhKtenq9nvr6ejQaDfF4nGAwyPz8PG63m0gkgsFgEJ0GqbRNyuVyMjIyaGtr\n4+zZsxw/fhyVSsXMzAxDQ0N8+umnjI2NJX27LoRElEolSqUSmUyG1+vF6XRSUFBAVVUVLS0t1NbW\nkpeXh8ViQaVSif1jhFh7b29vShO224t7srOzycnJoby8nAsXLtDY2Eh+fj6xWAylUrmjknFjY4OV\nlRVGRka4d+9eSmLtP/YZ9gMKhQKLxcKJEyf4zW9+Q1ZW1jPj/N+tdAREn/bs7KwYwt2NRc++FmyB\nhYUFvvrqK7xeL+fOnePMmTOUlJRQU1OD0WjEbDZz48YNHjx4gN/v39WVl0ajoaqqisrKSrGMfHFx\nkeHh4Zdq1yoIrmBpfN5YrOADTSQSHD58GIvFwsLCwg4HQnZ2NtnZ2chksh1isrKygtfrpa+vj8XF\nRebm5piYmMDlcmE2mykpKcFkMhGPx3G73SktR9fr9ZSWlvL73/+exsZGcfu+vr6Ox+NhdXU1JX3N\nheKIcDhMKBRCr9dz8eJFmpqaSCQSaLVajEYjOp2Ozc1NQqEQ4XBYjHGGQiGmp6df+j55HoSHTFZW\nFjU1NVy5coWysjLMZrO4uBH6w9tsNg4dOiQ+jODbh2VeXh51dXX4fL4dzqZUsl9W2Nv5sR3xs17T\narVUVVXR2NjI48ePGR0d3ZUeKQdCsCORCEtLS6J53uv10tDQQGNjI8XFxbS3t6NSqdBqtdy8eXNX\nV4QKhYLMzEwyMzPRarVEo1GmpqYYHBx8LjudMJny8/NpampCq9WytrYmFgVNTEw817gmJia4ceMG\nKpWK3NxcsffH9gkm+KiXlpZYXV1lbW1NFD1BtIUfl8slxuo0Go1YLJDMUu9nITRTqqiowGw2i6/P\nzMzw6NEj/H5/SkRE8DIPDAxw7do1Tp48SVZWFllZWYRCIbG/xNzcHD6fD7lcTnNzM0VFRQBiiXqq\nHnZCzD07O5vjx49z9uxZTp06RTQaZWFhAZfLhcvlwu12s7a2xvHjx8nIyMBkMokhEo1GQ21tLevr\n6/h8PoaHh/F6vSlvXbtdGBOJhNhOOdkl/d9lc3OTlZUVHj58iMFgoKKiglgsxsrKyo5rIoxV6HVT\nWFhITk4ORqORjIyMHR0RX5YDIdjw7RZDMNY/efKE+vp6/umf/omSkhLRpJ6RkcHg4CCBQGDXtqCC\ni0KIUa2vr7/Qykno6tfS0sKFCxcwGAw4nU4ePnzItWvXmJqaeq5xDQ8Ps7S0RDAYpLa2ltzc3Gf+\nXjAYpK+vj9nZWRYWFkTb4bMoLi6mpaUFi8VCLBZLepx4O8JWvqKigldffRW9Xr9jFzIyMsLdu3dT\n5gpKJBJEo1Hu37/PxsYGZrOZ4uJisfLzyZMn9Pb2cvfuXXw+H3a7HZvNRm5uLjKZjKGhoZQ23ddq\ntdhsNpqbm3n33Xd58803icfjdHR08Pnnn/PkyRPGx8fx+XxkZ2djsVg4evSo2EtdcLsID8qFhQUS\niQTDw8MEg0GxPgK+FbJkPzS3O6nUajWlpaXY7XaxcjMVDxBBczo6Onj69Cmtra1iIdyzkocZGRmc\nOHGCy5cvi6HI3ebACLZAPB7H5/Px6NEj2tvb2draEreBwmkugUAgKf5cYZu8vr7+XHFJoUDm1KlT\nvPXWW5w4cYLNzU36+/u5deuWWIr/vAQCAW7dusWjR49+0BYolCWHw2Gi0eiPxn5tNpvYVEloUpWq\nPi1KpRK73U55eTllZWXi5wmHw4yMjDA6OprSB4jA8vIy9+/fx+l0ii0IBOub3+9ndXUVs9mMw+Gg\noqICq9XK6uqq6H1ONsLOrba2ljNnznD+/HkqKiqIRCKMjY3R1dXFzZs3xXusoKCAf/zHf+T06dPY\n7XbkcjlDQ0M8ffpU/P5tNhu/+c1vaGho4PHjx9y5cwe32y3eO8vLy0l3kGxfwWq1Wmpra6msrMRm\ns71w8/8XZX19ndnZWdbW1ojH4z/omHK5XPj9fkpKSnj11VeTMpYDI9jCCtVqtZKVlSW2YtyevBO2\nscn6MoXMr9fr/Vl/Q/Dn5ufnU1tby8WLFykvL2d5eZl79+5x+/Zt+vr6XjjBIzTO2i00Gg3p6eko\nFAqcTifffPNNygQ7IyODixcv8sorr4gWMyEO/Ne//pX+/v6UxYK3E41G8Xg8eDyeH/wdi8WC0Wgk\nPT0dtVpNNBplbGzsR//PbiCTydDpdJSVlXH69Glef/11amtrWVlZEXusdHV1sbCwIJ568sorr4gH\nbKyurjI0NERXVxdDQ0PYbDaxmZndbqesrAyr1YrD4RA7zEUiEW7fvk1XV1fSPpfb7WZiYgKHwyH2\nPBfOFq2pqRH976liY2ND9Kb/1O+9rG3vp9j3gi2Xy0VXQ2FhIYcPHxZj11VVVWJsKBAIMDc3x/j4\neNKe/oIP+OdMROEBI8TYz549S3NzM3Nzc3z55Zf8x3/8R8qa2L8Ii4uLjIyMpEwkzWYzV65coaGh\nQawQ83q99Pb28qc//em5Y/yp5LvJqPX1db755pukf78KhYKsrCxef/113nzzTZqbmwkGg3R3d/Px\nxx/T3d3N8vKyeDLR5cuXefPNN8nLyyMQCDA0NMQf/vAHHjx4wPz8vGj1FCyAR48eFf35CoVC7Env\n9/uTKtjT09M8fPhQPKRCoKCggObmZgYHB5PeBEroW/1z4/fC4qy2tpacnJykjWtfC7ZSqcRsNlNT\nU0NTU5PYDEo4tkpIQmxtbREOh8XY9W4/4YQJKWzNfihevJ2ysjKOHTvGuXPnqKioIDMzk6dPn/LZ\nZ5/x6aef7kl5/X5GeChvt5pNTEzQ1dXF8vLyvj5Ud2lpidHR0aQflvFdiouLefXVV7l06RIlJSW4\n3W4+//xzOjs7efLkCVarlba2Nqqrq6mrq6O0tBSDwUBnZyePHj2ip6eHoaEhPB6PWPcwPz/P2toa\nIyMj9Pb2cvToUWpra9Hr9SwvL/PFF19w+/btpH6uQCCA2+3eEx+4EGLKycnBZDIxMzNDKBT6yfBn\nYWEhra2tvPfeexw+fDhp49t3gi1Y0EwmEw6Hg+rqalpaWjh8+DAlJSVkZmaKK7BwOIzT6RQ9ug8e\nPEhqVl6pVIpjqq+vx+VyiSEDo9FIVlaWeC5lXV0dR44cob6+HrVajdvt5sGDB9y7d4/h4eGkjXG3\nUKvV6PX6Xctu/xh5eXk0NjaKjf4F0XM6nQwNDREMBvfEXvZzUSgU3ztlKBWUlZVx9uxZKisrMRgM\nuFwuNjc3sVqtNDU1UVRUJJ6IXlhYSCAQYGRkhKtXr/Lw4UMmJyd3JOi3H74hHGG1tLTE06dPRVfT\n119/nfRkqhCOO3fuHJmZmeJDPCsrS7ScLi8v77oXX6/Xi/23hYMnvtt2YjsKhQKNRoPJZKKtrY2L\nFy9y9OhRsrKyxBDt5ubmrnrw951gC9VF9fX1nDx5ktdff11sIyogTGifz8fAwAB//vOf6erqSlr5\nr+BWELY9bW1tBINBvvrqKzGGXFZWRltbG0eOHKGwsBCr1YpGo8Hr9TI5OUl/fz83b95kcnIyKWPc\nTWQymfgASkWP5CNHjvCrX/0Ks9m8w861vLzMzMzMvulp8UNkZ2dTUVHxvZLvZFNVVcWZM2fExmHp\n6em89tprnDp1Cr1ej91uFws9EokEDx8+5MMPP+Qvf/kLbrf7J3cDc3NzzM3N0dHRkfTPsp2JiQnk\ncjnvvvsu2dnZYliktLQUgA8++EDs5b2bZGVl0dzczNtvv01rayubm5s8fvwYj8fzzA6LarUaq9XK\n4cOHuXz5Mm+88QZarVYsTY/FYkSj0R0Om5dlXwi2UJklVOydP3+e+vp6SkpKsNvtO07EECbxo0eP\n6OvrY3h4mPn5+aQneARkMhklJSXodDrq6upYW1sjkUiQn58v2qV0Oh2hUIipqSnGx8fFJM3i4mLK\nD1t4EbYiJw0vAAAGlUlEQVT7S5MpQEJZb1lZmehO2dzcxO/3i72jA4HAvl5dw99WZtt3B6kgEokQ\nCAQwGAwolUqxuyF8mwAbGxvD6XQyOzvLxMQET548YWRkJOV95F+EeDwuxsu3l4In8/o2Njby61//\nmoaGBmw2G8FgkPb2dgoLC59Z9JKbm0tpaSkVFRUUFxeLD2yPx8PU1BS9vb18+eWXu3r4x54JtuBv\nttvt5Ofnk5WVRU5ODmVlZZw7d45Dhw6JK4dwOIzP52NpaYnx8XGGhoa4f/++ePp0Mr9EoTx+ZWUF\nv9+PXq/HYrGQmZlJQUGBmJQTuqBFo1Fx5d/T07OjSdVBQqvVfq+f724jnMqem5tLbm4uaWlpYnXl\nl19+yeDgIJFIZN8LttDvXPANp4rJyUlu3bpFWVkZJpMJlUollpgLcfXp6WlmZmYYGxsTWxfs9+sJ\n3z6Mnj59SkVFBQUFBeLryZzrubm51NXVkZeXJ67qT5w4QW1t7TPj6Xa7nUOHDpGdnY1cLicSibC6\nuiq2XO7p6WF4eHhXTRB7JthyuRytVsvJkyd5++23xeog4dTx7bFTQQD/+te/0t3dzdjYGOFwOCUG\n+lgsxszMDOPj48zPz4vFE0J4RDivTSaTEQ6Hcbvd3Lt3j/fff59r166xtbV1ICbId7FYLBw6dCip\nJ2cL94BwSrdcLicQCDA7O8vHH3/M6Ojonhya8Lx4vV6mpqaIx+NJ35Vs59atW4yMjNDS0iJa8Px+\nP48fP6anp4dAICDaXAW3w34s+34WoVCI+/fvU1dXx9GjR/dkDHq9nldeeeUHr5tcLhd/QqEQLpeL\n3t5ePvjgA65evUokEtn1+zelgq1SqcRzDKurq3E4HNTV1VFZWYnRaESr1YoCIWyNBwcHuX37Nvfv\n32diYgK3200oFEpZuezm5iarq6vcunWL9fV12traaGlpoby8XJyYoVCIgYEBcRs/OjrK2NjYnp6B\n+DIIopOKhON3BU5I1sTj8QMh1vDtSSnz8/MsLS1htVpRKBSYzWb0ev2u9I/4IYSTdx48eMDIyAga\njWZHL+ztQn3QEGoMFhYW8Hg83ztuKxn09PTwv//7v7S1tVFTU0N+fv4zd5gbGxv4/X6i0SjhcFjs\nLbR97q+vryfl2qdUsIUS0/b2dtrb23E4HKLrIxqN4vf7xfPOfD6feEZiV1cXAwMD4rYzlQiWQcH+\ntLS0hMfjYX5+XizY8fv9dHd38/DhQ4aGhva9De2HCIfDrK2tpUwot9sxg8Hgvji9+0UQdlZPnjzB\nZrOJfbSdTifBYDBphz8IBzrMzMzs+nvvNUJZeF9fH9nZ2WKnPKfT+cKVwT+F0AXS6/WKTeSys7PR\naDRsbW2JDpqVlRVmZmbw+/2sra0xOzvL4OAgQ0NDSbcjplSwBR/zkSNHaGhoEEMfQoLhyZMnTExM\nsLm5SXd3N/fu3WN1dZVwOJz0CqKfIhKJiE/7mzdv7ggVCBnhWCwmrmoOIktLS4yMjCStrPa7bGxs\n4Ha7mZ6eZnp6mrKyspT83WQQCATo6OjAbrdz4sQJLl68KDZRcjqd+97pst8QYvF/+ctfuHbtmljI\nIlgPkyGKq6urYnWtcELM22+/jcPhYGNjg2+++Ub86enpEXv5CBWOqZj7KRXsYDBIV1cXLpeLTz75\nRHxd+BJ8Ph8rKyskEgkWFxfFY6/2w5ZO6CMi9BL5v8jMzAyff/45c3NzosslmRVlQtjr5s2bLC4u\nYjKZxJOGvnu81X5nfX2dvr4+ampqKCkpoaioiJMnTxIKhbh69Soej+fAhHj2C1tbW6yvr6dsvgkL\nL0Gc/X4/Q0NDGI3GHfelx+PB5XIRCoVSXtwjS5YYymSyvVdZCYkUIRx0cf78ed555x1aW1uJRCL0\n9vby7//+72KiXELi55BIJJ6Zud4XPmwJiYOO0HhM6Gzn9Xo5ceIEjY2N5OTksLCwIAm2xEsjrbAl\nJHYR4Si7mpoaSktL0Wg0dHR0sLCw8H82lCax+/zQClsSbAkJCYl9RsoFW0JCQkJid0l+ZYSEhISE\nxK4gCbaEhITEAUESbAkJCYkDgiTYEhISEgcESbAlJCQkDgiSYEtISEgcECTBlpCQkDggSIItISEh\ncUCQBFtCQkLigCAJtoSEhMQBQRJsCQkJiQOCJNgSEhISBwRJsCUkJCQOCJJgS0hISBwQJMGWkJCQ\nOCBIgi0hISFxQJAEW0JCQuKAIAm2hISExAFBEmwJCQmJA4Ik2BISEhIHhP8H8pS7yD5yyasAAAAA\nSUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51a65ee690>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# we use a little trick to tile the first eight images\n", "imshow(solver.net.blobs['data'].data[:8, 0].transpose(1, 0, 2).reshape(28, 8*28), cmap='gray'); axis('off')\n", "print 'train labels:', solver.net.blobs['label'].data[:8]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test labels: [ 7.  2.  1.  0.  4.  1.  4.  9.]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAWwAAABKCAYAAACfHW4mAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51a4030a50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["imshow(solver.test_nets[0].blobs['data'].data[:8, 0].transpose(1, 0, 2).reshape(28, 8*28), cmap='gray'); axis('off')\n", "print 'test labels:', solver.test_nets[0].blobs['label'].data[:8]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Stepping the solver\n", "\n", "Both train and test nets seem to be loading data, and to have correct labels.\n", "\n", "* Let's take one step of (minibatch) SGD and see what happens."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": true}, "outputs": [], "source": ["solver.step(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Do we have gradients propagating through our filters? Let's see the updates to the first layer, shown here as a $4 \\times 5$ grid of $5 \\times 5$ filters."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(-0.5, 24.5, 19.5, -0.5)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAATQAAAD7CAYAAADkSGhKAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nLtEpzAAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519c0eab90>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["imshow(solver.net.params['conv1'][0].diff[:, 0].reshape(4, 5, 5, 5)\n", "       .transpose(0, 2, 1, 3).reshape(4*5, 5*5), cmap='gray'); axis('off')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. Writing a custom training loop\n", "\n", "Something is happening. Let's run the net for a while, keeping track of a few things as it goes.\n", "Note that this process will be the same as if training through the `caffe` binary. In particular:\n", "* logging will continue to happen as normal\n", "* snapshots will be taken at the interval specified in the solver prototxt (here, every 5000 iterations)\n", "* testing will happen at the interval specified (here, every 500 iterations)\n", "\n", "Since we have control of the loop in Python, we're free to compute additional things as we go, as we show below. We can do many other things as well, for example:\n", "* write a custom stopping criterion\n", "* change the solving process by updating the net in the loop"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration 0 testing...\n", "Iteration 25 testing...\n", "Iteration 50 testing...\n", "Iteration 75 testing...\n", "Iteration 100 testing...\n", "Iteration 125 testing...\n", "Iteration 150 testing...\n", "Iteration 175 testing...\n", "CPU times: user 12.6 s, sys: 2.4 s, total: 15 s\n", "Wall time: 14.4 s\n"]}], "source": ["%%time\n", "niter = 200\n", "test_interval = 25\n", "# losses will also be stored in the log\n", "train_loss = zeros(niter)\n", "test_acc = zeros(int(np.ceil(niter / test_interval)))\n", "output = zeros((niter, 8, 10))\n", "\n", "# the main solver loop\n", "for it in range(niter):\n", "    solver.step(1)  # SGD by <PERSON><PERSON><PERSON>\n", "    \n", "    # store the train loss\n", "    train_loss[it] = solver.net.blobs['loss'].data\n", "    \n", "    # store the output on the first test batch\n", "    # (start the forward pass at conv1 to avoid loading new data)\n", "    solver.test_nets[0].forward(start='conv1')\n", "    output[it] = solver.test_nets[0].blobs['score'].data[:8]\n", "    \n", "    # run a full test every so often\n", "    # (<PERSON><PERSON><PERSON> can also do this for us and write to a log, but we show here\n", "    #  how to do it directly in Python, where more complicated things are easier.)\n", "    if it % test_interval == 0:\n", "        print 'Iteration', it, 'testing...'\n", "        correct = 0\n", "        for test_it in range(100):\n", "            solver.test_nets[0].forward()\n", "            correct += sum(solver.test_nets[0].blobs['score'].data.argmax(1)\n", "                           == solver.test_nets[0].blobs['label'].data)\n", "        test_acc[it // test_interval] = correct / 1e4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Let's plot the train loss and test accuracy."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x7f5199b33610>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAEZCAYAAADR8/HkAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\noNypLaxMionScphoFW0KZqMgQqHyujjfwcv11mClRz3/********************************\n7LaT4k0zruUHwHySObqloF+B/oFIr+XKtgGvAT8lOde2vcd99Pvpg6iSpEHcy0twkqQHmAEkSRqE\nASRJGoQBJEkahAEkSRqEASRJGoQBpFFJcrZ9fTzJ33WC/Lfnfn/WWJJm8zkgjVKSeeDtqtpxG8es\nqqpr/7D/clU98n98f9IYOAPSqCTp2wofAF5oTdD2JnkoycEkC60y8672/vkkZ5IcBxbbts9adfHF\nvsJ4kgPAmna+jybHSudgkgvpmgO+MnHuL5IcS/JzkiN392pIw7pnS/FId0g/5X8XeKefAbXAuVRV\nzyZ5GPg6yan23meAzVX1W3v9RlVdTLIGWEjySVXtT7KnVR6fHutl4GngKWAd8F2Sr9q+OboGYX8A\nZ5NsqyqX7jQKzoA0VtNN0LYDr7eaWt8CjwFPtH0LE+EDsDfJeeAbuurCG1YY63ng41b0eRn4EthC\nF1ALVfV7q8l1Hlj/H34m6b7iDEi66a2q+nxyQ/us6MrU65eArVX1Z5LTwOoVzlvcGnj97OiviW3X\n8W9SI+IMSGN1GZi8YeAksDvJKoAkG5OsnXHco8DFFj6bgK0T+672x085A7zaPmdaB7xIVwZ/Vitq\naTT8b0tj0888fgSut6W0w8AhuuWvH1qflWW6/jTTHSVPAG8mWQJ+oVuG631IVxr/+6ra2R9XVZ8m\nea6NWcC+qlpO8iS3dqT0tlSNhrdhS5IG4RKcJGkQBpAkaRAGkCRpEAaQJGkQBpAkaRAGkCRpEAaQ\nJGkQBpAkaRA3ABGGQ9Z+SfjXAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519bf16690>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_, ax1 = subplots()\n", "ax2 = ax1.twinx()\n", "ax1.plot(arange(niter), train_loss)\n", "ax2.plot(test_interval * arange(len(test_acc)), test_acc, 'r')\n", "ax1.set_xlabel('iteration')\n", "ax1.set_ylabel('train loss')\n", "ax2.set_ylabel('test accuracy')\n", "ax2.set_title('Test Accuracy: {:.2f}'.format(test_acc[-1]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The loss seems to have dropped quickly and coverged (except for stochasticity), while the accuracy rose correspondingly. Hooray!\n", "\n", "* Since we saved the results on the first test batch, we can watch how our prediction scores evolved. We'll plot time on the $x$ axis and each possible label on the $y$, with lightness indicating confidence."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nWjSakdGi0YyMFo1mZLRoNCPz/yU19i71FpCwAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199aaab50>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n1ERCBQAAUBMJFQAAQE3/D63jLyWOsr2WAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199a4ba10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51999dd210>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519994c650>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAEBVJREFUeJztnVuMJNdZx39f36d6+rI9O7PDrveSlQKysSX7xSA5EREK\n0fqFwEsiS0hRgIgHboIHTHiJHyMkIsQLEoqNwkWJEMgoIAG2ERJBKIDROnYgjmPJK8/sXHd2unu6\np+99eOg+h+qanktX12Snqs5PKk13zXTpm93/fOec73zfd0QphcUyC4lHbYAlfFjRWGbGisYyM1Y0\nlpmxorHMjBWNZWZ8i0ZE7ojIuyLyAxF5MUijLBcb8ROnEZEk8H3gk8B94L+AF5RS3wvWPMtFxK+n\neRZ4Xyl1TynVA74BfDo4sywXmZTPz10D1lzv14GfcP+AiNhQc8hRSsm0+349jRVEjPErmvvAddf7\n64y8jSUG+BXNm8BHReSWiGSAzwLfDM4sy0XG15xGKdUXkV8D/glIAi/blVN88LXkPtOD7UQ49AQ9\nEbbEGCsay8xY0VhmxorGMjNWNJaZsaKxzIwVjWVm/G5YWk5ARMwFkE6nzZVKpRgOhwwGA/r9PoPB\nwLzXXy86VjTnQCKRIJlMkkwmSaVSlMtlKpWKuVqtFo1Gw1ytVmviGg6Hj/pXOBErmnMgkUgYz5LJ\nZFhZWeHWrVvcvHmTmzdvUqvV2N3dNdf+/j7VahWlFO12+1GbfypWNOeA9jDZbJZcLsfKygq3b9/m\nqaee4sknn2RnZ4e1tTU+/PBDstks6XQapRSdTscMaReZuUQjIveAOjAAekqpZ4MwKuwkEgkymQy5\nXA7HcahUKly9epXbt2/zxBNPUCqVEBE6nQ4HBwc0m03q9TqpVCr6omGUjPUJpdTDIIyJCslkknQ6\nzcLCAouLiywsLJDJZEgmkwAMh0P6/T69Xo9Op0Ov16Pf7zMcDglDbX0QS+6L/6fxQyaZTJLJZE4U\nzWAwoNfr0e126Xa7RjRhYF7RKOANEXlTRL4QhEFR4CyiOc7ThIF5h6fnlFKbIrIMvC4i7yqlvhWE\nYWFGiyaXy02IJpFIoJQyXqbdbnN4eEir1aLX6zEYDKI/PCmlNsdfd4FXGZW2xB49EXZ7mnQ6TSKR\nYDgc0ul0aDabVKtVHjx4QLVapdls0u12oy0aEXFEpDB+nQc+BbwTlGFhxj085fN5IxoRYTAY0O12\naTab1Go19vb2qNVqRjRhYJ7h6Qrw6niJmAL+Uin1WiBWhRzvnCaXyxnRuD1NrVbjwYMHNJtN+v0+\n/X4/FJ7Gt2iUUh8ATwdoS2jRsRW93+QWTLlcxnEcUqmUCeC1Wi2azSYHBwfUarVQRIHd2IhwACQS\nCVKplNlvKhaLVCoVVlZWuHr1KoVCgVQqRavVMtsGjUYjNHMYL1Y0AaCDeZlMhkwmQ6lUYmlpiStX\nrnDt2jVEBKWU8TAPHz6k0WjQ6XQetem+sPk0AeBeYufzeUql0oSnKZVKJJNJ42m0aMLqaaxoAkB7\nGi2aQqHApUuXWFpaYmVlhcXFRUSEZrMZieHJiiYAdCqE3qDM5XJkMhkTmxkMBrRaLZMSsb+/H6q4\njBcrmgDQniabzeI4jokA68nxNNFoTxNGrGgCQHuabDbLwsICuVzO5MlME03YIsBerGgCQE+EHceh\nUCjgOA7ZbNbkx/T7fVqtFvV6nb29PSsaC+RyOUqlEisrK9y4cYPl5WXy+TyJRMIE8w4PD01A7/Dw\nkE6nQ7/ff9Sm+8KKJgCy2SzFYpGVlRUee+wxLl++PCEavZutE8kPDw/pdruh2dX2YoN7AZDNZimV\nSiwvL3Pjxg2KxSKLi4skk8ljPY3OoQkjp3oaEXlFRLZF5B3XvYqIvC4i74nIayJSPl8zLx7u2qZc\nLkexWGR5eZmrV69SqVRwHGfC02jh6JKVMHuaswxPfwrc8dz7XeB1pdSPAv88fh8b9F6TjgIvLCzg\nOI4J7DmOM5F0pdM7dUGcvsIoGDiDaMaZePue2z8LfG38+mvAzwVs14XmONEsLi5SKBQmMvXcInFX\nUiqlQisav3OaK0qp7fHrbUa5NbHBLRp3spX2NDphXCllcn+93iasgoEAJsJKKRW3/nq6GE4LxnEc\nMzzl83k6nc4RwXivMON3yb0tIqsAIvIjwE5wJl18UqmUSRovlUrk83lyuRyp1OhvUM9jdMWBu9A/\nCvgVzTeBz41ffw7422DMufiIyIRoyuWySenUEWB3Vwi3aMI8JLk5y5L768C/Az8mImsi8nngy8DP\niMh7wE+P38cGr6dxiwame5qwT37dnDqnUUq9cMy3PhmwLaFBF/fn83nK5TL5fN7sNcHRYriwVVCe\nho0I+2BaiYq7grLT6VCv19nd3WV7e5udnR2TQB4FT2P3nnzg7gqhJ8Fe0dRqNdNSZHt7m2q1Grqq\ng+OwovGBN71T1zVp0XS7XeNp1tbWIudp7PA0I3r1dNLw1G63jWjW19d58OCBFU3c0GmbqVSKdDpN\nuVw2SeOrq6uUSiUymQyDwYBGo2GK4KrVKvv7+xwcHJgi/yhgRXMKIkIymSSbzZp2aLrSYHl5mdXV\nVfL5/BHR1Ov1I6IJS9ntaVjRnAEdl9Gbkl5Pk0gkTHF/o9GgXq9PeBqdCmE9TYxwx2WKxeJETdPq\n6qrJmdGX19P0er3Qp0O4saunM6A9TaFQoFwuUygUTEDP20Lk8PCQdrs9EdCLSiRYY0VzCiIy0XTx\n0qVLFIvFIxUHw+GQbrdLq9U6IpqoCceK5gy4PY0WjTs+Yz2Nh2NyhF8SkXURuTu+vOmgkUJ7Gu/w\n5N6kHAwGdDqdqaKJmnD85ggr4CtKqWfG1z8Gb9rFQC+5j+uhB9Dr9Tg8PDSdrXQxXK/Xi4xQ3PjN\nEYYY9Q8+rcWrWzTestsoMs+c5tdF5Dsi8nLUS1i8u9reDcper2e6de7u7lKr1UxBXBTxK5o/Bj7C\nqOfeJvAHgVl0ATlteNITYDs8nYBSakeNAb5KxPoH6/Oa3GUqOhpcKpVYWFiY6AbRaDSo1Wo8fPjQ\niEZXUUYRX6IZJ5Nrfp6I9Q/Wk1/dR08PS4VCgWKxSC6XM6LRVZP1ep39/X2zox3l4enUbYRxjvBP\nAZdFZA34EvAJEXma0SrqA+BXztXKHzLTPI0Wje6fl0wmTQsR7Wm0aHSKZ1SHJ785wq+cgy0XAl2f\n7fY0enjSonGfP9npdI4MT1HZYzoOu2E5hUwmw+LiotmgrFQqE1sHrVbLzGfa7TbNZpN2uz2xMRll\n7DaCBxEhm82yuLhIpVLhypUrLC0tUSqVcBzHbBv0ej3T3Uo3KYpSbdNJWNF40G3qC4UCS0tLrK6u\nsrS0ZDxNOp0GmGiJ1mw2Q93ZalasaKZwkqfJZDLApGjcniYOWNFMwdvi1d2pUzOt7DbqcxmNFY0H\nETnSf0b3BNanrejJrj5uJ0yHlgaBFc0U3KLJZrNmn0lvG7i7W+ljBK1oYo7X06TTaVKp1FTRuIcm\nK5qY4k7vdCdduROu+v0+7XabRqNBtVql0WjQbrft6inOpNNpHMcx5zZ5l9vu/Bl9BmWUNyi92Iiw\nB+1pHMcx0WCdDqE9jRaNbluvl91xEc2JnkZErovIv4jI/4jId0XkN8b3I91HeJqn0Tk0gIkGa09T\nr9cjVXZ7GqcNTz3gt5RSPw78JPCrIvI4Eesj7E2yyufz5ggefaLKtImwXnbbibALpdSWUuqt8esG\n8D3gGhHqI6yHI90OTbeodx/2pRsA6DhN3DnznEZEbgHPAP9BxPoIu2u1S6WS6TquRaPza7SniTtn\nEo2ILAJ/A/ymUurA/RcX9j7C3m6duvGiWzTucxAsZ8vcSzMSzJ8rpXTr120RWVVKbYW9j/C01ZKu\noNSCUUqZeYtO8Ww2m+YonrAfkDErp62eBHgZ+F+l1B+6vhWpPsKZTMZ06tQ72nq15C651amd7h40\ntVrNJGHFJbh3mqd5DvgF4G0RuTu+90VGfYP/SkR+CbgHfObcLDxndP6M4ziUy2UuX748kXAFGNHo\nI3jcgqlWq3S7XVOGGwdOFI1S6t843htFoo+wFo0+hN0tGj08DYdDE5txexktnKiceXBWYhkRdk9o\ndXtXLZpKpWKO35kWAa5WqxNDkq44iMNcRhNL0cBk1YHucqVFoyPAqVQKpZSpoNRlt97TbrVg4iKc\nWIpGJ1q5RaNjNJcuXZrqaZrNpinw157GXQwXF8FAjEWjhTPN0+jewHoi3O12TYH/tFrtOAkGYioa\nL1pAWkR6SBIRut0u1WqVvb09tre32dzcZG9vj0ajYQ4DixuxFI32Dvryns+klDKrJRFhZ2eHra0t\nNjc3WV9fNzvbnU7nUf8qj4RYikbjFY1bOJp+v29OU9nY2OD+/fsmwBfVAv/TiK1o9LDijrG4S1L0\n1el0jGg2Nze5f/8+vV7PXHEklqJxz0OGwyHNZpO9vT02NjZwHMcIRx/ytba2xtbWFvv7+zQajdgF\n87zEUjQavRFZr9fZ2NggkUhwcHBghipdorK5ucnOzg4HBweR7NY5K7EVjf4PHwwG1Go1RIRms8nW\n1taRw9f1lkGj0TClt3EVDICc9MuLyHXgz4AVRg2M/kQp9Uci8hLwy8Du+Ee/6G0LG5YcG50aoWub\n3IeX6q/uOY7elIyDaJRSUxOIThPNKrCqlHprnIj134xSOz8DHCilvnLCZ6P/rxpxjhPNabvcW8DW\n+HVDRHSOMMSoj7BlkjMnvbpyhL89vhWbPsKWSc4kmvHQ9NeMcoQbxKyPsGWSE+c0YHKE/x74B0/K\np/7+LeDvlFJPee7bOU3IOW5O4ytHOOp9hC0nc9rq6WPAvwJvM1pyA/we8AKjocn0EXbVQenPWk8T\ncnwtuefBiib8+BqeLJZpWNFYZsaKxjIzVjSWmbGiscyMFY1lZqxoLDNzbnEaS3SxnsYyM1Y0lpk5\nV9GIyB0ReVdEfiAiLwbwvHsi8raI3BWR//Tx+VdEZFtE3nHd893e9pjnvSQi62Mb74rInRmeF2gL\n3hOe59tG4Gi1YVAXkATeB24BaeAt4PE5n/kBUJnj8x9nlEj2juve7wO/M379IvDlOZ/3JeC3fdq3\nCjw9fr0IfB943K+NJzzPt41KqXP1NM8C7yul7imlesA3gE8H8FzfaaZKqW8B+57bvtvbHvM88Gmj\nCrgF7wnP820jnO/wdA1Yc71f5/8N9osC3hCRN0XkC3M+S3Me7W3nToUNugVvkOm65yma81jLP6eU\negZ4nlH39I8H+XA18uPz2j13Kqy3Be+8NgadrnueorkPXHe9v87I2/hGKbU5/roLvMpoCJyX7XGp\njs5InKu9rVJqR40BvjqrjSe14PVjo+t5f6GfN6+N5ymaN4GPisgtEckAn2XUStYXIuKISGH8Og98\nimDSTANtbztPKmzQLXjPLV13ntXMGWbvzzOasb/PqApznmd9hNEK7C3gu36eB3wd2AC6jOZbnwcq\nwBvAe8BrQHmO5/0io4rUt4HvjP9zr8zwvI8Bw/HveHd83fFr4zHPe34eG5VSdhvBMjs2ImyZGSsa\ny8xY0VhmxorGMjNWNJaZsaKxzIwVjWVmrGgsM/N/z4EQsKT2Kt0AAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51998d0e10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nQE0kVAAAADWRUAEAANREQgUAAFDT/wMd8Optey5//QAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519984f250>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51997d3ad0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519bee6f50>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nT5UJ3wAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519c0b0c10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nmkioAAAAaiKhAgAAqImECgAAoKb/B3fcHurPRSGqAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519c0d5bd0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51996ff910>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nJhIqAACAmv4fGZAJwEI7dFcAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519969d110>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAGNNJREFUeJztnVmM7Fldxz+na1+6qrqW7q5ebt+ZuTMDTEzgBU2AyAMh\nQ0xAXyQkRoNofFA0SiLig6D4gCYQow9EZYlbQKOBgInKYETxwQUzw4CyzMxdeu/au/a1jw9dv8Op\nukvfWrvrzv+bnPyram7/51TVt37n9/ud3+97lNYaBw5GwdJlT8DB4sEhjYOR4ZDGwchwSONgZDik\ncTAyHNI4GBljk0Yp9axS6rtKqZeUUh+c5qQcXG2ocfI0SikX8D3gbcAB8N/Ae7TW35nu9BxcRYxr\nad4IvKy1vq217gCfB941vWk5uMpwj/l3m8Ce9Xwf+GH7HyilnFTzgkNrre71+riWxiHEqxjjkuYA\n2Laeb3NubRy8CjAuab4BPKmUuq6U8gLvBr40vWk5uMoYy6fRWneVUr8E/BPgAj7tRE6vHowVcj/U\njR1HeOExbUfYwasYDmkcjAyHNA5GhkMaByPDIY2DkeGQxsHIcEjjYGQ4pHEwMhzSOBgZDmkcjAyH\nNA5GxrhFWAAopW4DZaAHdLTWb5zGpKYNpdTAY7fbjcvlMtezszO01macnZ0NDHkd4LLamIffg1KK\npaUllFID87bnOitMRBrOi7HeqrUuTGMys4J8wEtLS7jdbiKRCMvLy0QiEUKhEN1ul06nY0az2aTR\naNBsNmk2m5ydndHr9cz1MuZvE8XtduPxeMzo9Xq0220zzs7OzN/OgkCTkgbgnjuhVwXyQbtcLlwu\nFz6fj5WVFdbX10mn0yQSCUOORqNBo9GgXC5zenpKuVwGoNPpoJSi0+lc6nuQ4fV6CQQCZnQ6Her1\nOrVajV6vN3OrOA1L81WlVA/4Y631n05hTlOHUsosRz6fj3g8zvb2Nk888QRbW1tUq1UzKpUK2WwW\nj8cDQLvdNkvDZViZ4fcgxA+FQoTDYZaXl2m1Wiil6PV6tFqtAeLMApOS5k1a6yOlVAp4Tin1Xa31\n16cxsUkhX/TS0hI+n8/8KiORCOl0mu3tbR5//HF2dnY4PT01lqVUKqG1pt1uU61WzRcl/sNlvAeP\nx4PP5zMjHA4TjUaJRqNEIhHq9TpKKbrdLvV63SylskxNm0ATkUZrfdS/ZpVSX+C8teXSSSNfsFIK\nr9dLIpEglUqRSqVYXV1lZ2eH7e1tVldXiUQixlfpdrs0m03c7vOPRZ53Oh263e6ArzBLDPswy8vL\nxONxEokEiUSCSCRCOBw2o1gs4vP50FpTr9cH3k+32536/MYmjVIqCLi01hWlVAh4O/DbU5vZmLA/\nbDHliUSCnZ0dHnvsMa5du0YqlSKZTJJKpYhEIvR6PTqdDu12m3q9bpambrdLq9Wi2+2aX++83oPt\nw4TDYdLpNNeuXePatWtEo1GCwSDBYJBAIMDJyQlnZ2fU63Xy+bx5L1rrmSxVk1iaNeALfRPqBv5K\na/2VqcxqQgyv/0KaZ555hqeffppQKEQwGCQUCuHz+cyH3Gq18Pv9d1kaCWXnRRpgwHlfXl5mfX2d\nJ598kte97nXEYrG7lqt6vU6xWOTg4IBGo2EIM4sldWzSaK1vAa+f4lwmgu3D2D5ANBollUoZx/ep\np54a+BVrrc1VTLodfrfb7bm/F3HavV4vXq/XRHs7Ozs89dRTRCIRQ6qlpSWq1SqRSIRAIIDH45m5\nDzaNkPvSIR+QECYWi5mRSqXY2dkhlUoRDodxu920221DiGazyeHhIUdHR2bs7e2Ry+Wo1+uX8n68\nXi/Ly8tmbGxskEwmiUajBAIBAOr1ukkV7O7ucnx8TLFYpFar0Wq1aLfbM4uiFp409vrvcrnw+/2s\nrKyQTqdJp9Nsbm5y/fp1VldXCYfDuFwuut2uCa9LpRJ7e3tm7O/vk8/nKRQKl0qaSCRinPeNjQ1S\nqZTxZTqdDrVazcxTSFMoFKjVajSbTeOHOaS5D4bzMLFYjI2NDR5//HEee+wx1tbWjKVxuVx0Oh2q\n1Sq5XI5MJsPu7i63bt3i1q1b3Llzx/yCm83mpbwfmzTb29tsbm4a0tjJvGw2y97e3j0tjR1yTxsL\nTxo7te71evH7/cRiMeMDPPHEEyanEQqFWFpaotPpUKlUyOVyHBwcmA/+9u3b3Llz59Leh/ggfr+f\naDTK6uoqW1tbrK+vE4/HCYVCuN1uut2uSULu7u5ycHBANpulVCrRaDRm7octPGkAY2ECgYDxAyKR\nyABZfD4fLpcLrTWNRoNiscjR0RG7u7tkMhlOT09ptVqXMn+JkmREo1ESiQRra2tsbW2RSCRMlFcs\nFsnlcuRyObLZLNlslkKhQKVSodVqzWVDdeFJI7vWPp+PYDBoCBOJRIjFYkSjURNJyY62kOb4+Jjd\n3V1yudylkkYceImWIpEIiUSC9fV1tra2CIVCZu+rVCoNECaTyVAoFKhWq2ZZmjUeOdLIfoxtaexf\nca/Xo9FoUCqVjKWpVqvGF7gM2GkC2eqIx+Osra2xubmJy+WiVqtRr9ep1+sDpMlmsxSLRVqtlkOa\nizCcl/H7/YRCIWNlpPwhHA4P1Ma0Wi0qlQqFQoFMJsPh4eFAXmbe81dK4fF4CAQCZlsgHo+bjPXa\n2ppJD7TbbbM85fN5MyqVCr1eb+YblYKFI81wAZLs+MqvU/ZmAoEAbrebs7MzY0mq1SrFYpG9vT2y\n2SyVSoVOp2O2COZVYDXsw9h7Y6lUihs3brCxsWGSeN1u15Rs5PN5isUi1WrVhNZ2sdg8sHCkgcEU\nu9frJRgM3pc0vV6PWq1GJpMhl8txfHxsknfVapVOpzP3D932YTweD/F4nM3NTbO3tLW1RTqdJhKJ\nmLzSMGkqlQqNRmNgX2weVXuw4KSxfRkhjZ05tS1NNpvlzp077O7uGksjpLFLPecBl8s14MMIaW7c\nuMFrXvMaEokEsVjsoUgjlhLmV4p6IWmUUp8BfgzIaK1/qP9aHPhrYAe4Dfyk1ro0w3na8xn40CVi\nisViJBIJkskkkUgEv99vHF8hzd7eHq+88gonJyfk83lqtdpMSgcuwrAfFo/HSafTXL9+naefftps\nd3g8HrTWtFotarUap6enA6Sxl6e5zv8h/s1ngWeHXvsN4Dmt9VPAP/efzwUul4tQKDSwCSklD5ub\nm6ytrRGJRPB4PCaJd3p6SrFYJJ/Pm/C6Xq9fWvmmXX0nDnsoFCIQCOD1eg1RTk9POTk54fj4eOAq\nzu+88jLDuNDSaK2/rpS6PvTyO4Ef7T/+M+BrzIk4LpeLcDhMMpk0db6bm5tmpFIpPB4PbrfbRB3D\npKlUKtTr9UuxMvCDZGQoFCIajRrS+P1+Q5pms2lCbJswJycnZo9pXiH2XfMf8+/WtNYn/ccnnNfW\nzAVLS0uEQiGSySRbW1vs7OyYzcl0Os3KyoqpWJM9pmHSNJtNWq3WlbI0wWDQkKbVatFsNs28hTCZ\nTIaTkxMT9Q13HswLEzvCWms9T309l8tFMBgkHo+zsbHBtWvXWFtbMyMSiVCpVIw1qVarpva3UCiQ\nz+dNOeS8CsXt8k1gwBdbWVkhGo0SDofN8iSbpaVSiePj4wErc5klG4JxSXOilFrXWh8rpdJAZpqT\nehjcby2XCjuxNFI7Yye/Zh0p2Yk7ON+A9Pv9BAIB/H4/6+vrbGxsmJFOp4nH4wSDQZRSNJtNCoUC\nBwcHvPLKK2Z/rFqtXmpHhGBc0nwJ+Bng9/rXL05tRmPCJoNdgSc1vhJlzCu0tpOQgUBgoDAsnU4P\nkCaZTLKyskIgEEApRaPRoFAosL+/z0svvcTx8TH5fJ5qtXopy9EwHibk/hznTm9SKbUH/BbwMeBv\nlFLvox9yz3KSF8EmgVgau1hcugmGk2AzlMMdKA4LBoOsrKyYJVTIsrm5ycbGhtnuENLYluall14i\nn8+bRr6FsDRa6/fc5z+9bcpzGQvDhLFJIxZGliX5Iu3Hw71BYx5lNPDYrt+VFIHkYiQ1YFsbr9dr\n+soBY2kODw+5efOm6fS8KljIjPCDIG2rwWDQEGdzc9NU6IdCobsa/GWHWGprR4Xb7R4YUuIgY9iH\nSSaTpqhKEnh2z3ipVDIh9VU8N/2RI400yEmVnpj7s7MzU3RuR09SBVcul6lUKlSr1ZH/n+Lo2g6v\n9CQFAgHTpCdDCsUk8yu+lyxBUrZ5WSH1RXgkSePxeAx5JFnmdrsJh8Osrq4asojagpQa5HI50yg3\nCuxuRxk2MeLxOCsrK8TjceLxuMnHyIaldEVI9rpUKlGtVk3D21XDQpPG9kPksU0WwCwBktupVCrG\n15EOysPDQ4LBoGmSGxVSIWhfZUi/tT2G+5FkiSyXy+RyuQFL45BmCrAr705OTvD7/abJX9LxduQC\n5z5HIBCg1+uZTUwZIiNiF3OPCrEuouQgVxnBYBCfz2dIOexTCVkODw/Z39/n4ODAbBU4y9MUIDW+\nQhrZLZZlwC7vhB8sV36/HzhvD7G/sF6vh8/nIxKJkEwmp+LT+Hy+u557vV4zJ0k+Si5JOiMODw+5\nffs2h4eHJsx2LM0UYJPG7XYbVYWVlRXq9Trtdtv8om31K2lZHc4Ka62JRCImcppG9CTDlmiTq1LK\nkEb6x21Lc/v2bVP361iaKcEmjSTr4vE4q6urA2GqXd0nX6LP57srpyKY5y9aSCMbk8OkOT09NZGU\nQ5opQGttPnCXy8Xp6SlHR0f4/X601hQKhbuWBilokqu9hMhyZQs02vp1D7MTbveGdzodEz3Jddjx\nFcUq2VCVUL9Wq9FoNMwO/DxLUEfBQpKm0+kMZHaPjo6MoM/x8bHJj0iuRHIn8ppENJIIlP0qCcWl\nCF2+yItQq9XMqFarA8m8cDh8T9KIFo7kh6RFRUgjKYGriIUkjRRPiTOptaZWq5HL5Uxtiq1BIzUr\ny8vLRKNRo6MXCATu2naQGpxisUihUKBUuriKtVgsDowbN24AGDGiYdiWRso4qtXqAGnmXew+Csat\nEf4I8HNAtv/PPqS1/sdZTdKGkEaI02g0qNVqZLPZgYo4O8kmSbWVlRWSyaQJsWOxmLmnkEa09gqF\ngil8ughS6yKj2+0SDodZX1+/55cupKnVapTLZcrl8l2kucp4GEvzWeCPgD+3XtPAJ7TWn5jJrEaA\nXT8jkPyMvWlpZ1wlxD06OiIejw+Ev51Oh0KhYEaxWLxwDpLFlUL14T4qe9ui1+tRLBbJZDIcHByw\nv7/P4eHhlc7LDGPcGmG4QvrBNnHEzzk7OxvY06lWq8anyeVy7O/vm/pc+wsV7ZpRfBopH202m/ds\nvLPFoaWJP5PJsLe3x82bN029TL1ev5LL0TAm8Wner5T6ac4Pdv/AvFpYhmFr4dkRkERYEqHYCt92\n8k0cYTuCkvzJKHkb29kdLvYSAovAtSQm9/f3uXXrFoVCwXRIPMqk+STwO/3HHwU+DrxvKjMaA+KT\nCGRrwMa98jP306QbtbZmOCNsE1lEoWVTslarGUuzv7/PzZs3zeakhNlXHWORRmttvEOl1KeAL09t\nRlPC8Bc+y1+waOTZOn8icQKYzgLZTRcRonK5bHSK5yk5OynGIo1SKi3C08BPAN+a3pQWDxKJSTnn\n+vo6sVjM7He1Wi3TWSAbkrlcjnK5TLvdvqfzfJUxTo3wh4G3KqVez3kUdQv4hZnO8opDpGfX1ta4\nfv36XaSxlbdu3rzJ4eGhUa2QRN6iEAbGrxH+zAzmsrCwLc21a9cGSCOVgyKiJBuSInkm0q2LhIXL\nCF8FSBQme1m2AJF0eUrxl123I86w7C3NS4Ro2nBIMwakElCG1P6KTyNbGCJ1Mpw8XCT/5V5wSDMG\npHBdNP1swqytrZmWFBFVsjPTw201iwiHNCNCKgGDwSCxWMwsTba1sZOMYmnsDVFb8mwR4ZBmDAhp\nRH3L1iqWsxds/0UOIZPd7GazOdOzC2YNhzRjQNQ4ZQd9eXnZlJMCRu5M9q+KxaI5uU52s+XYwEWE\nQ5oxIDXHYmnsY3MAc3ZBuVweqMuRgivZ2LwsUaVJ4ZBmDIilGSaNfbiYCCtKeYWQxhaHhMs753sS\nOKQZA/dankQY8uzsbKBYXPaZpFh8kfaY7oeHEWp0MITh5WmYNI1Gg9PTU7OTLQd2NJvNhbQsw3As\nzYiwZemFNKKXN3z2QjabfSRJ80BLo5TaVkr9i1Lqf5VS31ZK/XL/9bhS6jml1PeVUl9RSsXmM92r\nAQm57eXJ5/OxtLRkSHM/S/Mo4KLlqQP8qtb6GeBHgF9USr2WS9QRvgwISaLRqJE6k7OxRVzRbrlt\nt9sm5BYFCBGKfhQszQOXJ631MXDcf1xVSn0H2OQSdYTnDfvQDulwECn9YDA4IE0iFYRSmyylptIu\nvKh5mWE8tE/TLy5/A/CfXKKO8GVABAKkFSaVShGLxQiFQqZDU6Iiux5YSGOLRT7ylkaglAoDfwf8\nita6MtQDPVcd4XlDLM3y8jKJRIJ0Ok0ymSQWixEMBvF6vYYMdhmEbWmktfdRIAw8XOWeh3PC/IXW\nWqRfL11HeJ4Q0iSTSaOZJ2G2FI7b8mfVatUcQGpbF1uE2h5298Ii5HAuip4U8Gng/7TWf2D9J9ER\nhiuiIzwr2JYmmUySTqdJJBIsLy8bta1OpzOQAS6Xy3cd2GGfHS7FW16v12jXiGzK/TokrhIusjRv\nAn4KeFEp9Xz/tQ9xxXSEZw3xaRKJBBsbG/ckjew13Ys0tq6wLRUrQ0omhltxriouip7+nftboyuh\nIzxriIafbWlERmTY0sjZmOVy2RzgJfcY1suxdXNEAWMRCANORvihIF+4dGfaywkMhtoirmjndoY1\ncWSpEjLVajWjHnFVxRltOKSZATweD6FQyEjbh0KhgfPChyv75ORe6SO/6s6wQ5opYFjDzy4HbbVa\nrKysmKMSk8mkadMVy+Tz+YyY0qPgCDsYESJ0LZZGaz1wiMbGxoZxnBuNhjm7qVqtksvlHNK8WuB2\nu40sbSwWw+VyDTwXSxOJRPD5fCZ7LMJGktNxHOFXESSPI1YmFouZRF+9Xjc5GXF6S6US+XzejKOj\nIwqFwiMvNeLAgtfrNYdjBIPBu9Q+7VNe6vW6kRqRowbl2OR6vX7lnWBwSDMVSAgeDAYHBBblKod1\n5HK5AX2ag4MD9vb2zHmbV1WhfBgOaR4Cw+KNtg8ie0f2LrfdFNfpdMjlcmSzWTOOjo7IZDLm8Ay5\nn11wfpXhkOYCiD6xHA/o9/tN6CyJOFusWvSBbW3hUqk0MMSXkWo+u+tyEeCQ5gIIafL5vJFGs4/V\ncblcxuGVIb1OoitsC1MPX6Wib5FEAR5IGqXUNudSsKucCxj9idb6Dy9TR3je0FrTaDTI5/MAZimR\nSCkQCBgtYDmh7uTkxJylfXx8bEQf5Tq8fC1aD9RFlkZqhF/oF2L9j1LqOa6QjvA8ID6NUopOpzNw\nbpMcvWOPTCYzMERexK7esx3lRcO4NcJwhXSEZw0RrxanVyRERFpfcjKyTA0fKSj+yrwOk5811MNO\nvl8j/K/AM8AHgPcCp9xHR/hRKgGVsyZlDJ8iJ5GP5GZsEsnxO3YYDpMd6TwvaK3vaRgeijT9pelr\nwO9qrb+olFrlB/7MR4G01vp9Q39zdT+NEWGXMSil7jr4azgvYyug20vQIhDFxtik6dcI/z3wD0Ml\nn/LfrwNflsM2rNcX45NxcF/cjzRj1Qj3i8kFr3od4VcbHmhplFJvBv4NeJHziAngN4H3AAM6wlYf\nlPytY2kWHBP5NOPAIc3iY6zlyYGDe8EhjYOR4ZDGwchwSONgZDikcTAyHNI4GBkOaRyMjJnlaRw8\nunAsjYOR4ZDGwciYKWmUUs8qpb6rlHpJKfXBKdzvtlLqRaXU80qp/xrj7z+jlDpRSn3Lem1sedv7\n3O8jSqn9/hyfV0o9O8L9pirB+4D7jT1H4O7m9WkNwAW8DFwHPMALwGsnvOctID7B37+Fc7HJb1mv\n/T7w6/3HHwQ+NuH9Pgz82pjzWwde338cBr4HvHbcOT7gfmPPUWs9U0vzRuBlrfVtrXUH+Dzwrinc\nd+wyU63114Hi0Mvv5FzWlv71xye8H4w5R631sdb6hf7jKmBL8I48xwfcb+w5wmyXp01gz3q+zw8m\nPC408FWl1DeUUj8/4b0Es5C3fb9S6ptKqU+Pq+Y+bQle637/MekcZ0maWcTyb9JavwF4B+fq6W+Z\n5s31uR2fdN6fBB7jvN7oCPj4qDcYluCddI79+/1t/37VSec4S9IcANvW823Orc3Y0Fof9a9Z4Auc\nL4GT4kQptQ6mInEieVutdUb3AXxq1Dk+SIJ3nDla9/tLud+kc5wlab4BPKmUuq6U8gLv5lxKdiwo\npYJKqeX+4xDwdqZTZjpVedtJSmGnLcE7s3LdSaKZh/De38G5x/4y512Yk9zrMc4jsBeAb49zP+Bz\nwCHQ5tzfei8QB74KfB/4ChCb4H4/y3lH6ovAN/tf7toI93szcNZ/j8/3x7PjzvE+93vHJHPUWjvb\nCA5Gh5MRdjAyHNI4GBkOaRyMDIc0DkaGQxoHI8MhjYOR4ZDGwchwSONgZPw/UDzRgG/E2K8AAAAA\nSUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199603550>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAEzBJREFUeJzt3XuwXWV5x/HfL/eTGzGEatVcqbaQ0YpVB0UkgHSoo2hb\nq9JWre3Y6ag1peqIjO2MM+1odTpSx2lnLNQL3tqKRZ1WJQUSY6jEYMIlCQoV2qBIQhPIOblxSJ7+\nsXfC4eTsnPc5K+/Ze8fvZ4Zhr7WfvdZ71vuedZ6stfb7OCIEAACAiZvS7QYAAAD0OxIqAACAhkio\nAAAAGiKhAgAAaIiECgAAoCESKgAAgIamdXPntpmzAQAA9I2I8FjrqyZUti+VdLWkqZKuiYi/GR3z\nwQ9+8LjPrVu3ThdccMHobWX2m2rnkSNHimMz83ZltpuJzag5z1inNm/YsEHnnXfeU9YdPny48XbH\n8sQTTxTHZuMff/zx4tjh4eHi2AMHDhTHHjx4sDh26tSpxbGd4rdv366zzjrruPWzZs0q3u6UKeUX\nvjP98eijj1aJzcbv27evOPbQoUPFsZnx1qmv9+/fr9mzZx+*****************************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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199591d10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\njYuZ4ZLGxcz4f041SDwzkyB1AAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51994fb990>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nhkioAAAAGiKhAgAAaOj/AYSDQCwV4p2TAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519948af90>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(8):\n", "    figure(figsize=(2, 2))\n", "    imshow(solver.test_nets[0].blobs['data'].data[i, 0], cmap='gray')\n", "    figure(figsize=(10, 2))\n", "    imshow(output[:50, i].T, interpolation='nearest', cmap='gray')\n", "    xlabel('iteration')\n", "    ylabel('label')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We started with little idea about any of these digits, and ended up with correct classifications for each. If you've been following along, you'll see the last digit is the most difficult, a slanted \"9\" that's (understandably) most confused with \"4\".\n", "\n", "* Note that these are the \"raw\" output scores rather than the softmax-computed probability vectors. The latter, shown below, make it easier to see the confidence of our net (but harder to see the scores for less likely digits)."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nWjSakdGi0YyMFo1mZLRoNCPz/yU19i71FpCwAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51991d5790>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51990357d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198fc5750>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198f2af90>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198ebf310>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nkqSeDKgkSZJ6MqCSJEnqyYBKkiSpp/8HAhusMCiNzYsAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198e2cfd0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198dc0d90>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\noZIkSerIhEqSJKkjEypJkqSO/h9QtS7j/64B5gAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51994b6550>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nT5UJ3wAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51994cc7d0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nv+YAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199160590>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199196050>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\naSADKkmSpIH+H12Zh6umpBoZAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f51991d5f90>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nSUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519c085f10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519998c390>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAI0AAACPCAYAAADHlliuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\njYuZ4ZLGxcz4f041SDwzkyB1AAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f519c0f7b50>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlQAAACbCAYAAACkuQVhAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAEaBJREFUeJzt3X+QndVdx/HPJ7vZzebHBgIkJCQYVFAIrYAtJEChWHSQ\nKbRqLUVtY3HqOG1txJYpZUb/0mkt45R2OjpTQVp+tFVppe0oFFSglFrCj00IJA2JFA3EbBDYZEOy\nyWbz9Y97N2yW/XFOnpy994b3a2Yn93nu9z7n7HPuvfvN8+N8HRECAADA4ZvW6A4AAAC0OhIqAACA\nikioAAAAKiKhAgAAqIiECgAAoCISKgAAgIraG9m4beZsAAAALSMiPNb6ogmV7csk3SSpTdLNEfFX\no2MWLlz4htf19/drzpw5h6xra2vLaTernwcOHEiOHRoaKrLdnNiSc4flbHu8Pu/Zs0ddXV1JsWMp\ntY9z45shthXnicv9/KVqxX0B4M2j2Ck/222SvizpMklnSLra9uml2gMAAGiUktdQnStpc0Q8HxGD\nkr4p6T0F2wMAAGiIkgnVSZK2jFh+ob5uUh0dHUU6hKnR3t7QS/MAAJhyJROqw77gobOz80j2A1Ns\n+vTpje4CAABTquShhBclLRmxvES1o1SH6O/vP/i4o6ODZAoAALSckgnV45JOtb1U0lZJV0m6enTQ\n6Lv5AAAAWk2xhCoi9tv+uKTvqzZtwi0RsaFUewAAAI1S9OrhiLhH0j0l2wAAAGi0ht+ONTg4mBSX\nM6lfziSgufE5sdOmlbnmP2diyP3792dte9++fUW2nRNbapLMXDkTVOaMdU5syX1RaqJMJuAE8GZE\nLT8AAICKSKgAAAAqIqECAACoiIQKAACgIhIqAACAikioAAAAKiKhAgAAqIiECgAAoCISKgAAgIpI\nqAAAACoioQIAAKio4bX8Ojs7k+Jy6p/l1hIbGho6amNz67uVqh1XquZeybpxOdtutVgAwJFV9AiV\n7SW2H7D9jO2nbX+iZHsAAACNUPoI1aCkayNije3Zkp6wfX9EbCjcLgAAwJQpeoQqIrZFxJr6412S\nNkhaVLJNAACAqTZlF6XbXirpbEmPTlWbAAAAU2FKEqr66b67JK2qH6kCAAA4ahS/y8/2dEnfknRH\nRNw9+vmdO3cefNzZ2Zl81x8AAECzKJpQ2bakWyStj4ibxorp7u4u2QUAAIDiSp/yu0DS70m6xHZP\n/eeywm0CAABMqaJHqCLih2I2dgAAcJQj2QEAAKio4aVnpk+ffsS3mVuCI6csSs62c8rltLenD0Wp\nMi5S3u+XUwInZ1/kyN1u7bK+MttOlbPf9u3blxw7MDCQ1Y/9+/dnxafK2cc5sSVLLrVaiZ+c/Sbl\nvZfb2tqK9CMnNue9mfO+kJpj/EoqNdalyr81y+f6SOAIFQAAQEUkVAAAABWRUAEAAFREQgUAAFAR\nCRUAAEBFJFQAAAAVkVABAABUREIFAABQEQkVAABARSRUAAAAFTW89ExqaY2SJUM6OjqSYzs7O5Nj\nS5XVKFUyRMorRVCqDE/OPp41a1ZyrCTNnj07Obarqys59thjjy2y3U2bNiXHPvfcc8mxkvTKK68k\nx+aM9XHHHZcce/755yfHXnrppUX6IOXt5zvuuCM5dtu2bcmxOeWnTj755ORYSVqwYEFy7PLly5Nj\nlyxZkhw7ODiYHLtly5bk2Jyxk6Senp7k2Jdeeik5dteuXcmxOZ+n3DJDc+fOTY6dP39+cuwxxxyT\nHJvzPZsTm/MZkfL2XWoucuedd4773Li9s/1bkkLSWD2KiPh2SuO22yQ9LumFiLgi5TUAAACtZKJ0\n7wrVEqrxJCVUklZJWi9pTmqnAAAAWsm4CVVE/H7VjdteLOlySX8p6U+rbg8AAKAZTXphi+0Tbd9i\n+9768hm2/yBx+1+QdJ2kAxX6CAAA0NRSrhT+qqT7JC2qL2+SdO1kL7L9bknbI6JHY1+HBQAAcFRI\nuWT++Ij4B9vXS1JEDNpOuc3sfElX2r5c0gxJ3bZvi4gPjQzauXPnwcednZ1Zd3gBAACU0tvbq97e\n3qTYlIRql+2D9yDbXi5px2QviogbJN1Qf83Fkj41OpmSpO7u7qSOAgAATKUFCxYcMu3IunXrxo1N\nSag+Kel7kn7W9o8knSDpfYfRr/SJNwAAAFrIpAlVRDxh+yJJv6DatVAbIyJ9hrbaNh6S9NDhdREA\nAKC5TZpQ2e6S9FFJF6p2lOlh238bEQOlOwcAANAKUk753SZpp6QvqXaE6nck3S7ptwv2CwAAoGV4\nsppCttdHxBmTrTusxu1YuHBhamzydnPq0eXGl4rNqSeYE5tb1/DAgfQpw1JrH0l5dbxy5NRAlPL2\nR6lajDn1qE488cTk2JxaYpK0e/fu5Nicels5tcRy6gmm3mmTu10prz5mzmck5zsgZx/PmZNXeGLG\njBnJsaVqheZ89gYG0k+A9PX1ZfUjZ9s5n+uc90VOLb+Scr7jStXTLfU39XDiU/T39ysixtwZKb/1\nk7ZXDC/U7/J74kh1DgAAoNVNVBx53YiYR2xvUe0aqpMlbZyCvgEAALSEyYojAwAAYBITFUd+fuSy\n7fmqzXgOAACAEVKKI19pe5Okn6o2l9Tzku4p3C8AAICWkXJR+l9IWiHp2Yg4RdK7JD1atFcAAAAt\nJCWhGoyI/5M0zXZbRDwg6W2F+wUAANAyUibEedX2HEkPS7rT9nZJeRPeAAAAHMVSjlC9V9JuSddK\nulfSZnEHIAAAwEEpxZGHj0YNSfpq0d4AAAC0oHFLz9jepdpEnmOJiOiu3LgdixcvTo3N2W5WP3LK\nC5QqRVCqbEHuvmiGUgSlYqVy45dTWicntlRJFCnvfZSznzs7O5Njc95DOaWOcsun5MTn7LdmKTEC\n4MgZr/TMRPNQpReWGoftYyTdLGmZasnZNRHx46rbBQAAaCbpVVoPzxcl/WtEvM92u6RZhdsDAACY\ncsUSKttzJb0jIlZKUkTsl7SjVHsAAACNkncBSp5TJL1k+1bbT9r+O9szC7YHAADQECUTqnZJ50j6\nm4g4R9Jrkq4v2B4AAEBDlEyoXpD0QkQ8Vl++S7UE6xA7duw4+DMwMFCwOwAAAGUUu4YqIrbZ3mL7\ntIh4VtKlkp4ZHTd37txSXQAAAJgSpe/y+2PVytV0SPovSR8u3B4AAMCUK5pQRcRaSW8v2QYAAECj\nlbyGCgAA4E2h9Cm/Se3duzcprq2tLXmbJcut5JTgKLXdUiVtpHKlWZqhzIkktbenv+Vzxi/nhoqc\n2FIlUUrK+f2a4fMk5X2/5HxGmmVM8OaR+/cvFe/lyXGECgAAoCISKgAAgIpIqAAAACoioQIAAKiI\nhAoAAKAiEioAAICKSKgAAAAqIqECAACoiIQKAACgIhIqAACAihpeeqajoyMpLqcEx+DgYFYfckpJ\nlJJTKiO3rEaOnFIuqWMn5ZVDyBnrffv2JcdK0muvvZYcm/M+ynkPzZgxIzk2p1ROTvkUKW/f5YxJ\nqfJFpbabK+e9PH369OTYnM/ezJkzk2OlvM9qTomR/v7+5NickkQly/vkxJcqt1KyjFJXV1dy7Pz5\n85Nj582blxyb832R8x7q6+tLjpXy3nOp34cT/V0oeoTK9mdsP2N7ne2v284rvAYAANACiiVUtpdK\n+oikcyLiLZLaJH2gVHsAAACNUvKU305Jg5Jm2h6SNFPSiwXbAwAAaIhiR6gi4hVJfy3pfyRtldQX\nEf9Wqj0AAIBGKXnK7+ck/YmkpZIWSZpt+3dLtQcAANAoJS9Kf5ukH0XEyxGxX9K3JZ0/Omjnzp0H\nf/bu3VuwOwAAAOkOHDigoaGhgz8TKXkN1U8k/ZntLkkDki6VtHp0UHd3d8EuAAAAHJ7R01ZMNCVL\nyWuo1kq6TdLjkp6qr/5KqfYAAAAapejEnhHxeUmfL9kGAABAo1F6BgAAoCISKgAAgIoaXstv1qxZ\nSXG7d+9O3mZu/aWcWkmlajDl1GHLqfmVU0tMyqszN2fOnOTYnJp0Oft4z549ybFSXv26nH7k/H45\n+3jhwoXJseedd15yrCSdeeaZybE5n7+NGzcmx27YsCE5dtOmTcmxOeMhSWeddVZy7DXXXJMcm1Mr\nrbe3Nzl29eo33N8zoZwxeeyxx5JjX3755eTY2bNnJ8cuW7YsOXbFihXJsZJ0wQUXJMcuWrQoObbU\nDVa5tWa3bt2aHNvT05Mcu3nz5uTYnO+LnL+TqfnCsJy/f6nvz5UrV477HEeoAAAAKiKhAgAAqIiE\nCgAAoCISKgAAgIpIqAAAACoioQIAAKioKROqnFsu0Xz6+voa3QUcprVr1za6C6ggZ3oENJcHH3yw\n0V1ARSRUOOJ27NjR6C7gMJFQtTYSqtZFQtX6mjKhAgAAaCUkVAAAABU5t0zLEW3cblzjAAAAmSJi\nzLpkDU2oAAAAjgac8gMAAKiIhAoAAKCipkuobF9m+ye2N9n+dKP7g/HZ/nvbvbbXjVg3z/b9tp+1\nfZ/tYxrZR4zP9hLbD9h+xvbTtj9RX88YNjnbM2w/anuN7fW2P1tfz9i1ENtttntsf6++zPi1sKZK\nqGy3SfqypMsknSHpatunN7ZXmMCtqo3VSNdLuj8iTpP07/VlNKdBSddGxDJJyyV9rP55YwybXEQM\nSLokIs6S9FZJl9i+UIxdq1klab2k4YuZGb8W1lQJlaRzJW2OiOcjYlDSNyW9p8F9wjgi4mFJr45a\nfaWkr9Uff03Se6e0U0gWEdsiYk398S5JGySdJMawJUTE8AzIHZLaVPssMnYtwvZiSZdLulnS8F1j\njF8La7aE6iRJW0Ysv1Bfh9axICJ66497JS1oZGeQxvZSSWdLelSMYUuwPc32GtXG6IGIeEaMXSv5\ngqTrJB0YsY7xa2HNllAxh8NRJGpzcjCmTc72bEnfkrQqIvpHPscYNq+IOFA/5bdY0kW2Lxn1PGPX\npGy/W9L2iOjR60enDsH4tZ5mS6helLRkxPIS1Y5SoXX02j5RkmwvlLS9wf3BBGxPVy2Zuj0i7q6v\nZgxbSETskPQvkn5ZjF2rOF/SlbZ/Kukbkn7F9u1i/FpasyVUj0s61fZS2x2SrpL03Qb3CXm+K2ll\n/fFKSXdPEIsGsm1Jt0haHxE3jXiKMWxyto8fvgPMdpekX5XUI8auJUTEDRGxJCJOkfQBSf8RER8U\n49fSmm6mdNu/Lukm1S6yvCUiPtvgLmEctr8h6WJJx6t2vv/PJX1H0j9KOlnS85LeHxF9jeojxle/\nK+wHkp7S66cWPiNptRjDpmb7LapdtDyt/nN7RNxoe54Yu5Zi+2JJn4yIKxm/1tZ0CRUAAECrabZT\nfgAAAC2HhAoAAKAiEioAAICKSKgAAAAqIqECAACoiIQKAACgIhIqAA1n+5H6vz9j++ojvO0bxmoL\nAI4k5qEC0DRsv1O1SQ6vyHhNe0Tsn+D5/oiYcyT6BwDj4QgVgIazvav+8HOS3mG7x/Yq29Ns32h7\nte21tv+wHv9O2w/b/o6kp+vr7rb9uO2nbX+kvu5zkrrq27t9ZFuuudH2OttP2X7/iG0/aPufbG+w\nfcfU7g0Arai90R0AAL1e+ubTkj41fISqnkD1RcS5tjsl/dD2ffXYsyUti4j/ri9/OCJerde2W237\nroi43vbHIuLsMdr6TUm/JOmtkk6Q9JjtH9SfO0vSGZL+V9Ijti+ICE4VAhgXR6gANBOPWv41SR+y\n3SPpx5LmSfr5+nOrRyRTkrTK9hpJ/ylpiaRTJ2nrQklfj5rtkh6S9HbVEq7VEbE1atdErJG0tMLv\nBOBNgCNUAJrdxyPi/pEr6tdavTZq+V2SlkfEgO0HJM2YZLuhNyZww0ev9o5YNyS+KwFMgiNUAJpJ\nv6SRF5B/X9JHbbdLku3TbM8c43Xdkl6tJ1O/KGn5iOcGh18/ysOSrqpfp3WCpIskrdYbkywAmBT/\n6wLQDIaPDK2VNFQ/dXerpC+pdrrtSduWtF3Sb9TjR96ifK+kP7K9XtJG1U77DfuKpKdsPxERHxx+\nXUT8s+0V9TZD0nURsd326aO2rTGWAeAQTJsAAABQEaf8AAAAKiKhAgAAqIiECgAAoCISKgAAgIpI\nqAAAACoioQIAAKiIhAoAAKAiEioAAICK/h9eRJ9X5s2MkgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5199b2b090>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(8):\n", "    figure(figsize=(2, 2))\n", "    imshow(solver.test_nets[0].blobs['data'].data[i, 0], cmap='gray')\n", "    figure(figsize=(10, 2))\n", "    imshow(exp(output[:50, i].T) / exp(output[:50, i].T).sum(0), interpolation='nearest', cmap='gray')\n", "    xlabel('iteration')\n", "    ylabel('label')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. Experiment with architecture and optimization\n", "\n", "Now that we've defined, trained, and tested LeNet there are many possible next steps:\n", "\n", "- Define new architectures for comparison\n", "- Tune optimization by setting `base_lr` and the like or simply training longer\n", "- Switching the solver type from `SGD` to an adaptive method like `AdaDel<PERSON>` or `Adam`\n", "\n", "Feel free to explore these directions by editing the all-in-one example that follows.\n", "Look for \"`EDIT HERE`\" comments for suggested choice points.\n", "\n", "By default this defines a simple linear classifier as a baseline.\n", "\n", "In case your coffee hasn't kicked in and you'd like inspiration, try out\n", "\n", "1. Switch the nonlinearity from `ReLU` to `ELU` or a saturing nonlinearity like `Sigmoid`\n", "2. <PERSON><PERSON> more fully connected and nonlinear layers\n", "3. Search over learning rate 10x at a time (trying `0.1` and `0.001`)\n", "4. Switch the solver type to `<PERSON>` (this adaptive solver type should be less sensitive to hyperparameters, but no guarantees...)\n", "5. Solve for longer by setting `niter` higher (to 500 or 1,000 for instance) to better show training differences"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration 0 testing...\n", "Iteration 25 testing...\n", "Iteration 50 testing...\n", "Iteration 75 testing...\n", "Iteration 100 testing...\n", "Iteration 125 testing...\n", "Iteration 150 testing...\n", "Iteration 175 testing...\n", "Iteration 200 testing...\n", "Iteration 225 testing...\n"]}, {"data": {"text/plain": ["<matplotlib.text.Text at 0x7f5199af9f50>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAEZCAYAAADR8/HkAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f5198f46a10>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_net_path = 'mnist/custom_auto_train.prototxt'\n", "test_net_path = 'mnist/custom_auto_test.prototxt'\n", "solver_config_path = 'mnist/custom_auto_solver.prototxt'\n", "\n", "### define net\n", "def custom_net(lmdb, batch_size):\n", "    # define your own net!\n", "    n = caffe.NetSpec()\n", "    \n", "    # keep this data layer for all networks\n", "    n.data, n.label = L.Data(batch_size=batch_size, backend=P.Data.LMDB, source=lmdb,\n", "                             transform_param=dict(scale=1./255), ntop=2)\n", "    \n", "    # EDIT HERE to try different networks\n", "    # this single layer defines a simple linear classifier\n", "    # (in particular this defines a multiway logistic regression)\n", "    n.score =   <PERSON>.InnerProduct(n.data, num_output=10, weight_filler=dict(type='xavier'))\n", "    \n", "    # EDIT HERE this is the LeNet variant we have already tried\n", "    # n.conv1 = L.Convolution(n.data, kernel_size=5, num_output=20, weight_filler=dict(type='xavier'))\n", "    # n.pool1 = <PERSON>.Pooling(n.conv1, kernel_size=2, stride=2, pool=P.Pooling.MAX)\n", "    # n.conv2 = L.Convolution(n.pool1, kernel_size=5, num_output=50, weight_filler=dict(type='xavier'))\n", "    # n.pool2 = <PERSON>.Pooling(n.conv2, kernel_size=2, stride=2, pool=P.Pooling.MAX)\n", "    # n.fc1 =   L.InnerProduct(n.pool2, num_output=500, weight_filler=dict(type='xavier'))\n", "    # EDIT HERE consider <PERSON><PERSON> or <PERSON><PERSON> for the nonlinearity\n", "    # n.relu1 = L.Re<PERSON>U(n.fc1, in_place=True)\n", "    # n.score =   <PERSON>.<PERSON>roduct(n.fc1, num_output=10, weight_filler=dict(type='xavier'))\n", "    \n", "    # keep this loss layer for all networks\n", "    n.loss =  <PERSON><PERSON>(n.score, n.label)\n", "    \n", "    return n.to_proto()\n", "\n", "with open(train_net_path, 'w') as f:\n", "    f.write(str(custom_net('mnist/mnist_train_lmdb', 64)))    \n", "with open(test_net_path, 'w') as f:\n", "    f.write(str(custom_net('mnist/mnist_test_lmdb', 100)))\n", "\n", "### define solver\n", "from caffe.proto import caffe_pb2\n", "s = caffe_pb2.SolverParameter()\n", "\n", "# Set a seed for reproducible experiments:\n", "# this controls for randomization in training.\n", "s.random_seed = 0xCAFFE\n", "\n", "# Specify locations of the train and (maybe) test networks.\n", "s.train_net = train_net_path\n", "s.test_net.append(test_net_path)\n", "s.test_interval = 500  # Test after every 500 training iterations.\n", "s.test_iter.append(100) # Test on 100 batches each time we test.\n", "\n", "s.max_iter = 10000     # no. of times to update the net (training iterations)\n", " \n", "# EDIT HERE to try different solvers\n", "# solver types include \"SGD\", \"Adam\", and \"Nesterov\" among others.\n", "s.type = \"SGD\"\n", "\n", "# Set the initial learning rate for SGD.\n", "s.base_lr = 0.01  # EDIT HERE to try different learning rates\n", "# Set momentum to accelerate learning by\n", "# taking weighted average of current and previous updates.\n", "s.momentum = 0.9\n", "# Set weight decay to regularize and prevent overfitting\n", "s.weight_decay = 5e-4\n", "\n", "# Set `lr_policy` to define how the learning rate changes during training.\n", "# This is the same policy as our default LeNet.\n", "s.lr_policy = 'inv'\n", "s.gamma = 0.0001\n", "s.power = 0.75\n", "# EDIT HERE to try the fixed rate (and compare with adaptive solvers)\n", "# `fixed` is the simplest policy that keeps the learning rate constant.\n", "# s.lr_policy = 'fixed'\n", "\n", "# Display the current training loss and accuracy every 1000 iterations.\n", "s.display = 1000\n", "\n", "# Snapshots are files used to store networks we've trained.\n", "# We'll snapshot every 5K iterations -- twice during training.\n", "s.snapshot = 5000\n", "s.snapshot_prefix = 'mnist/custom_net'\n", "\n", "# Train on the GPU\n", "s.solver_mode = caffe_pb2.SolverParameter.GPU\n", "\n", "# Write the solver to a temporary file and return its filename.\n", "with open(solver_config_path, 'w') as f:\n", "    f.write(str(s))\n", "\n", "### load the solver and create train and test nets\n", "solver = None  # ignore this workaround for lmdb data (can't instantiate two solvers on the same data)\n", "solver = caffe.get_solver(solver_config_path)\n", "\n", "### solve\n", "niter = 250  # EDIT HERE increase to train for longer\n", "test_interval = niter / 10\n", "# losses will also be stored in the log\n", "train_loss = zeros(niter)\n", "test_acc = zeros(int(np.ceil(niter / test_interval)))\n", "\n", "# the main solver loop\n", "for it in range(niter):\n", "    solver.step(1)  # SGD by <PERSON><PERSON><PERSON>\n", "    \n", "    # store the train loss\n", "    train_loss[it] = solver.net.blobs['loss'].data\n", "    \n", "    # run a full test every so often\n", "    # (<PERSON><PERSON><PERSON> can also do this for us and write to a log, but we show here\n", "    #  how to do it directly in Python, where more complicated things are easier.)\n", "    if it % test_interval == 0:\n", "        print 'Iteration', it, 'testing...'\n", "        correct = 0\n", "        for test_it in range(100):\n", "            solver.test_nets[0].forward()\n", "            correct += sum(solver.test_nets[0].blobs['score'].data.argmax(1)\n", "                           == solver.test_nets[0].blobs['label'].data)\n", "        test_acc[it // test_interval] = correct / 1e4\n", "\n", "_, ax1 = subplots()\n", "ax2 = ax1.twinx()\n", "ax1.plot(arange(niter), train_loss)\n", "ax2.plot(test_interval * arange(len(test_acc)), test_acc, 'r')\n", "ax1.set_xlabel('iteration')\n", "ax1.set_ylabel('train loss')\n", "ax2.set_ylabel('test accuracy')\n", "ax2.set_title('Custom Test Accuracy: {:.2f}'.format(test_acc[-1]))"]}], "metadata": {"description": "Define, train, and test the classic LeNet with the Python interface.", "example_name": "Learning LeNet", "include_in_docs": true, "kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.10"}, "priority": 2}, "nbformat": 4, "nbformat_minor": 0}